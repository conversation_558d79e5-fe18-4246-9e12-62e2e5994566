{"cve_id": "CVE-2023-20233", "published_date": "2023-09-13T17:15:09.523", "last_modified_date": "2024-11-21T07:40:57.257", "descriptions": [{"lang": "en", "value": "A vulnerability in the Connectivity Fault Management (CFM) feature of Cisco IOS XR Software could allow an unauthenticated, remote attacker to cause a denial of service (DoS) condition on an affected device.\r\n\r This vulnerability is due to incorrect processing of invalid continuity check messages (CCMs). An attacker could exploit this vulnerability by sending crafted CCMs to an affected device. A successful exploit could allow the attacker to cause the CFM service to crash when a user displays information about maintenance end points (MEPs) for peer MEPs on an affected device."}, {"lang": "es", "value": "Una vulnerabilidad en la función Connectivity Fault Management (CFM) del software Cisco IOS XR podría permitir que un atacante remoto no autenticado cause una condición de denegación de servicio (DoS) en un dispositivo afectado. Esta vulnerabilidad se debe al procesamiento incorrecto de mensajes de verificación de continuidad (CCM) no válidos. Un atacante podría aprovechar esta vulnerabilidad enviando CCM manipulados a un dispositivo afectado. Una explotación existosa podría permitir al atacante provocar que el servicio CFM se bloquee cuando un usuario muestra información sobre los puntos finales de mantenimiento (MEP) para los MEP pares en un dispositivo afectado."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-ios-xr-cfm-3pWN8MKt", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}