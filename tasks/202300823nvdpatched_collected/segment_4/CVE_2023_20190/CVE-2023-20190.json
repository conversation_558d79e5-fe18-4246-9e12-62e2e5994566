{"cve_id": "CVE-2023-20190", "published_date": "2023-09-13T17:15:09.357", "last_modified_date": "2024-11-21T07:40:47.653", "descriptions": [{"lang": "en", "value": "A vulnerability in the classic access control list (ACL) compression feature of Cisco IOS XR Software could allow an unauthenticated, remote attacker to bypass the protection that is offered by a configured ACL on an affected device.\r\n\r This vulnerability is due to incorrect destination address range encoding in the compression module of an ACL that is applied to an interface of an affected device. An attacker could exploit this vulnerability by sending traffic through the affected device that should be denied by the configured ACL. A successful exploit could allow the attacker to bypass configured ACL protections on the affected device, allowing the attacker to access trusted networks that the device might be protecting.\r\n\r   There are workarounds that address this vulnerability.\r\n\r   \r\n\r \r This advisory is part of the September 2023 release of the Cisco IOS XR Software Security Advisory Bundled Publication. For a complete list of the advisories and links to them, see Cisco Event Response: September 2023 Semiannual Cisco IOS XR Software Security Advisory Bundled Publication ."}, {"lang": "es", "value": "Una vulnerabilidad en la característica de compresión de la clásica lista de control de acceso (ACL) del software Cisco IOS XR podría permitir que un atacante remoto no autenticado evite la protección que ofrece una ACL configurada en un dispositivo afectado. Esta vulnerabilidad se debe a una codificación incorrecta del rango de direcciones de destino en el módulo de compresión de una ACL que se aplica a una interfaz de un dispositivo afectado. Un atacante podría aprovechar esta vulnerabilidad enviando tráfico a través del dispositivo afectado que la ACL configurada debería denegar. Una explotación existosa exitoso podría permitir al atacante eludir las protecciones ACL configuradas en el dispositivo afectado, permitiéndole acceder a redes confiables que el dispositivo podría estar protegiendo. Existen workarounds que abordan esta vulnerabilidad. Este aviso es parte de la publicación de septiembre de 2023 del paquete de avisos de seguridad del software Cisco IOS XR."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-comp3acl-vGmp6BQ3", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}