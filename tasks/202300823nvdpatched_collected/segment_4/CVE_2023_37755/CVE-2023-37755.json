{"cve_id": "CVE-2023-37755", "published_date": "2023-09-14T20:15:10.477", "last_modified_date": "2024-11-21T08:12:13.307", "descriptions": [{"lang": "en", "value": "i-doit pro 25 and below and I-doit open 25 and below are configured with insecure default administrator credentials, and there is no warning or prompt to ask users to change the default password and account name. Unauthenticated attackers can exploit this vulnerability to obtain Administrator privileges, resulting in them being able to perform arbitrary system operations or cause a Denial of Service (DoS)."}, {"lang": "es", "value": "i-doit pro 25 e inferiores e I-doit open 25 e inferiores están configurados con credenciales de administrador predeterminadas inseguras, y no hay ninguna advertencia ni mensaje para pedir a los usuarios que cambien la contraseña y el nombre de cuenta predeterminados. Los atacantes no autenticados pueden aprovechar esta vulnerabilidad para obtener privilegios de administrador, lo que les permite realizar operaciones arbitrarias del sistema o provocar una denegación de servicio (DoS)."}], "references": [{"url": "https://github.com/leekenghwa/CVE-2023-37755---Hardcoded-Admin-Credential-in-i-doit-Pro-25-and-below/blob/main/README.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://medium.com/%40ray.999/d7a54030e055", "source": "<EMAIL>", "tags": []}, {"url": "https://medium.com/%40ray.999/i-doit-v25-and-below-incorrect-access-control-issue-cve-2023-37755-d7a54030e055", "source": "<EMAIL>", "tags": []}]}