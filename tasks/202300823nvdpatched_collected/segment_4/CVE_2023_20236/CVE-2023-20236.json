{"cve_id": "CVE-2023-20236", "published_date": "2023-09-13T17:15:09.607", "last_modified_date": "2024-11-21T07:40:57.700", "descriptions": [{"lang": "en", "value": "A vulnerability in the iPXE boot function of Cisco IOS XR software could allow an authenticated, local attacker to install an unverified software image on an affected device.\r\n\r This vulnerability is due to insufficient image verification. An attacker could exploit this vulnerability by manipulating the boot parameters for image verification during the iPXE boot process on an affected device. A successful exploit could allow the attacker to boot an unverified software image on the affected device."}, {"lang": "es", "value": "Una vulnerabilidad en la función de arranque iPXE del software Cisco IOS XR podría permitir que un atacante local autenticado instale una imagen de software no verificada en un dispositivo afectado. Esta vulnerabilidad se debe a una verificación de imagen insuficiente. Un atacante podría aprovechar esta vulnerabilidad manipulando los parámetros de arranque para la verificación de imágenes durante el proceso de arranque iPXE en un dispositivo afectado. Una explotación exitosa podría permitir al atacante iniciar una imagen de software no verificada en el dispositivo afectado."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-iosxr-ipxe-sigbypass-pymfyqgB", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}