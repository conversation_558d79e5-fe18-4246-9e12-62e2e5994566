{"cve_id": "CVE-2023-37611", "published_date": "2023-09-18T22:15:45.803", "last_modified_date": "2024-11-21T08:12:01.843", "descriptions": [{"lang": "en", "value": "Cross Site Scripting (XSS) vulnerability in Neos CMS 8.3.3 allows a remote authenticated attacker to execute arbitrary code via a crafted SVG file to the neos/management/media component."}, {"lang": "es", "value": "La vulnerabilidad de Cross Site Scripting (XSS) en Neos CMS 8.3.3 permite a un atacante autenticado remoto ejecutar código arbitrario a través de un archivo SVG manipulado en el componente neos/management/media."}], "references": [{"url": "https://github.com/neos/neos-development-collection/pull/4812", "source": "<EMAIL>", "tags": []}, {"url": "https://rodelllemit.medium.com/stored-xss-in-neo-cms-8-3-3-9bd1cb973c5b", "source": "<EMAIL>", "tags": ["Exploit"]}]}