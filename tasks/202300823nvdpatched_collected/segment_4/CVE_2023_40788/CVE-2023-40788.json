{"cve_id": "CVE-2023-40788", "published_date": "2023-09-19T00:15:34.993", "last_modified_date": "2024-11-21T08:20:07.743", "descriptions": [{"lang": "en", "value": "SpringBlade <=V3.6.0 is vulnerable to Incorrect Access Control due to incorrect configuration in the default gateway resulting in unauthorized access to error logs"}, {"lang": "es", "value": "SpringBlade &lt;=V3.6.0 es vulnerable al Control de Acceso Incorrecto debido a una configuración incorrecta en la puerta de enlace predeterminada, lo que provoca un acceso no autorizado a los registros de errores"}], "references": [{"url": "https://gist.github.com/kaliwin/89276ec7e97f9529c989bd77706c29c7", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://github.com/chillzhuang/SpringBlade", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/chillzhuang/SpringBlade/blob/master/blade-gateway/src/main/java/org/springblade/gateway/provider/AuthProvider.java", "source": "<EMAIL>", "tags": ["Exploit"]}]}