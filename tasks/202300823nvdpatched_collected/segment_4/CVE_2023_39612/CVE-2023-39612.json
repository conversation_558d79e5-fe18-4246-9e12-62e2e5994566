{"cve_id": "CVE-2023-39612", "published_date": "2023-09-16T01:15:07.397", "last_modified_date": "2025-03-27T14:42:46.480", "descriptions": [{"lang": "en", "value": "A cross-site scripting (XSS) vulnerability in FileBrowser before v2.23.0 allows an authenticated attacker to escalate privileges to Administrator via user interaction with a crafted HTML file or URL."}, {"lang": "es", "value": "Una vulnerabilidad de Cross-Site Scripting (XSS) en FileBrowser anterior a v2.23.0 permite a un atacante autenticado escalar privilegios a Administrador a través de la interacción del usuario con un archivo HTML o URL manipulada."}], "references": [{"url": "https://febin0x4e4a.wordpress.com/2023/09/15/xss-in-filebrowser-leads-to-admin-account-takeover-in-filebrowser/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/filebrowser/filebrowser/commit/b508ac3d4f7f0f75d6b49c99bdc661a6d2173f30", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/filebrowser/filebrowser/issues/2570", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}]}