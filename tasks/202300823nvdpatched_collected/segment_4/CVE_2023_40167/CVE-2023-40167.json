{"cve_id": "CVE-2023-40167", "published_date": "2023-09-15T20:15:09.827", "last_modified_date": "2024-11-21T08:18:54.840", "descriptions": [{"lang": "en", "value": "Jetty is a Java based web server and servlet engine. Prior to versions 9.4.52, 10.0.16, 11.0.16, and 12.0.1, Jetty accepts the `+` character proceeding the content-length value in a HTTP/1 header field.  This is more permissive than allowed by the RFC and other servers routinely reject such requests with 400 responses.  There is no known exploit scenario, but it is conceivable that request smuggling could result if jetty is used in combination with a server that does not close the connection after sending such a 400 response. Versions 9.4.52, 10.0.16, 11.0.16, and 12.0.1 contain a patch for this issue. There is no workaround as there is no known exploit scenario."}, {"lang": "es", "value": "Jetty es un servidor web y motor de servlet basado en Java. Antes de las versiones 9.4.52, 10.0.16, 11.0.16 y 12.0.1, <PERSON><PERSON> acepta el carácter `+` que precede al valor de longitud del contenido en un campo de encabezado HTTP/1. Esto es más permisivo de lo que permite el RFC y otros servidores rechazan habitualmente este tipo de solicitudes con 400 respuestas. No se conoce ningún escenario de explotación, pero es posible que se produzca contrabando de solicitudes si se utiliza jetty en combinación con un servidor que no cierra la conexión después de enviar dicha respuesta 400. Las versiones 9.4.52, 10.0.16, 11.0.16 y 12.0.1 contienen un parche para este problema. No existe ningún workaround ya que no se conoce ningún escenario de explotación."}], "references": [{"url": "https://github.com/eclipse/jetty.project/security/advisories/GHSA-hmr7-m48g-48f6", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://lists.debian.org/debian-lts-announce/2023/09/msg00039.html", "source": "<EMAIL>", "tags": ["Mailing List", "Third Party Advisory"]}, {"url": "https://www.debian.org/security/2023/dsa-5507", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://www.rfc-editor.org/rfc/rfc9110#section-8.6", "source": "<EMAIL>", "tags": ["Technical Description"]}]}