{"cve_id": "CVE-2023-28614", "published_date": "2023-09-15T17:15:14.170", "last_modified_date": "2024-11-21T07:55:40.330", "descriptions": [{"lang": "en", "value": "Freewill iFIS (aka SMART Trade) *********** allows OS Command Injection via shell metacharacters to a report page."}, {"lang": "es", "value": "Freewill iFIS (también conocido como SMART Trade) *********** permite la inyección de comandos del sistema operativo a través de metacaracteres del shell en una página de informe."}], "references": [{"url": "https://github.com/mandiant/Vulnerability-Disclosures/blob/master/2023/MNDT-2023-0012.md", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://www.freewillsolutions.com/smart-trade-ifis", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.kb.cert.org/vuls/id/947701", "source": "<EMAIL>", "tags": ["Third Party Advisory", "US Government Resource"]}]}