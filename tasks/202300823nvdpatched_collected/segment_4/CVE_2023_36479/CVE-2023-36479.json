{"cve_id": "CVE-2023-36479", "published_date": "2023-09-15T19:15:08.387", "last_modified_date": "2025-05-27T21:20:37.697", "descriptions": [{"lang": "en", "value": "Eclipse Jetty Canonical Repository is the canonical repository for the Jetty project. Users of the CgiServlet with a very specific command structure may have the wrong command executed. If a user sends a request to a org.eclipse.jetty.servlets.CGI Servlet for a binary with a space in its name, the servlet will escape the command by wrapping it in quotation marks. This wrapped command, plus an optional command prefix, will then be executed through a call to Runtime.exec. If the original binary name provided by the user contains a quotation mark followed by a space, the resulting command line will contain multiple tokens instead of one. This issue was patched in version 9.4.52, 10.0.16, 11.0.16 and 12.0.0-beta2."}, {"lang": "es", "value": "Eclipse Jetty Canonical Repository es el repositorio canónico para el proyecto Jetty. Los usuarios de CgiServlet con una estructura de comando muy específica pueden ejecutar el comando incorrecto. Si un usuario envía una solicitud a un servlet org.eclipse.jetty.servlets.CGI para un binario con un espacio en su nombre, el servlet escapará del comando envolviéndolo entre comillas. Este comando empaquetado, más un prefijo de comando opcional, se ejecutará mediante una llamada a Runtime.exec. Si el nombre binario original proporcionado por el usuario contiene una comilla seguida de un espacio, la línea de comando resultante contendrá varios tokens en lugar de uno. Este problema se solucionó en las versiones 9.4.52, 10.0.16, 11.0.16 y 12.0.0-beta2."}], "references": [{"url": "https://github.com/eclipse/jetty.project/pull/9516", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/eclipse/jetty.project/pull/9888", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/eclipse/jetty.project/pull/9889", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/eclipse/jetty.project/security/advisories/GHSA-3gh6-v5v9-6v9j", "source": "<EMAIL>", "tags": ["Exploit", "Patch", "Vendor Advisory"]}, {"url": "https://lists.debian.org/debian-lts-announce/2023/09/msg00039.html", "source": "<EMAIL>", "tags": ["Mailing List", "Third Party Advisory"]}, {"url": "https://www.debian.org/security/2023/dsa-5507", "source": "<EMAIL>", "tags": ["Third Party Advisory", "Mailing List"]}]}