{"cve_id": "CVE-2023-2680", "published_date": "2023-09-13T17:15:09.697", "last_modified_date": "2024-11-21T07:59:04.303", "descriptions": [{"lang": "en", "value": "This CVE exists because of an incomplete fix for CVE-2021-3750. More specifically, the qemu-kvm package as released for Red Hat Enterprise Linux 9.1 via RHSA-2022:7967 included a version of qemu-kvm that was actually missing the fix for CVE-2021-3750."}, {"lang": "es", "value": "Este CVE existe debido a una solución incompleta para CVE-2021-3750. Más específicamente, el paquete qemu-kvm lanzado para Red Hat Enterprise Linux 9.1 a través de RHSA-2022:7967 incluía una versión de qemu-kvm a la que en realidad le faltaba la solución para CVE-2021-3750."}], "references": [{"url": "https://access.redhat.com/security/cve/CVE-2023-2680", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2203387", "source": "<EMAIL>", "tags": ["Issue Tracking", "Vendor Advisory"]}, {"url": "https://security.netapp.com/advisory/ntap-20231116-0001/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}