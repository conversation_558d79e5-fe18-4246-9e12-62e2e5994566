{"cve_id": "CVE-2023-39914", "published_date": "2023-09-13T15:15:07.657", "last_modified_date": "2024-11-21T08:16:01.750", "descriptions": [{"lang": "en", "value": "NLnet Labs' bcder library up to and including version 0.7.2 panics while decoding certain invalid input data rather than rejecting the data with an error. This can affect both the actual decoding stage as well as accessing content of types that utilized delayed decoding."}, {"lang": "es", "value": "La biblioteca bder de NLnet Labs hasta la versión 0.7.2 incluida entra en pánico al decodificar ciertos datos de entrada no válidos en lugar de rechazar los datos con un error. Esto puede afectar tanto a la etapa de decodificación real como al acceso a contenidos de tipos que utilizaron decodificación retrasada."}], "references": [{"url": "https://nlnetlabs.nl/downloads/bcder/CVE-2023-39914.txt", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}