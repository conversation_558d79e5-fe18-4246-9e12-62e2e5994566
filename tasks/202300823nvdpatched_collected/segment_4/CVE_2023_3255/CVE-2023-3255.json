{"cve_id": "CVE-2023-3255", "published_date": "2023-09-13T17:15:09.877", "last_modified_date": "2024-11-21T08:16:48.617", "descriptions": [{"lang": "en", "value": "A flaw was found in the QEMU built-in VNC server while processing ClientCutText messages. A wrong exit condition may lead to an infinite loop when inflating an attacker controlled zlib buffer in the `inflate_buffer` function. This could allow a remote authenticated client who is able to send a clipboard to the VNC server to trigger a denial of service."}, {"lang": "es", "value": "Se encontró una falla en el servidor VNC integrado de QEMU al procesar mensajes ClientCutText. Una condición de salida incorrecta puede provocar un bucle infinito al inflar un búfer zlib controlado por un atacante en la función `inflate_buffer`. Esto podría permitir que un cliente remoto autenticado que pueda enviar un portapapeles al servidor VNC active una denegación de servicio."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2024:2135", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2024:2962", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/security/cve/CVE-2023-3255", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2218486", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch", "Third Party Advisory"]}, {"url": "https://security.netapp.com/advisory/ntap-20231020-0008/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}