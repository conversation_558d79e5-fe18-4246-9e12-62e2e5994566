{"cve_id": "CVE-2023-38891", "published_date": "2023-09-14T23:15:07.587", "last_modified_date": "2024-11-21T08:14:23.810", "descriptions": [{"lang": "en", "value": "SQL injection vulnerability in Vtiger CRM v.7.5.0 allows a remote authenticated attacker to escalate privileges via the getQueryColumnsList function in ReportRun.php."}, {"lang": "es", "value": "Una vulnerabilidad de inyección SQL en Vtiger CRM v.7.5.0 permite a un atacante remoto autenticado escalar privilegios a través de la función getQueryColumnsList en ReportRun.php."}], "references": [{"url": "https://code.vtiger.com/vtiger/vtigercrm/-/blob/master/modules/Reports/ReportRun.php#L395", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://github.com/jselliott/CVE-2023-38891", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}