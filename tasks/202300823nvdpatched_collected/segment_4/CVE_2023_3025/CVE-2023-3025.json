{"cve_id": "CVE-2023-3025", "published_date": "2023-09-16T09:15:07.447", "last_modified_date": "2024-11-21T08:16:16.200", "descriptions": [{"lang": "en", "value": "The Dropbox Folder Share plugin for WordPress is vulnerable to Server-Side Request Forgery in versions up to, and including, 1.9.7 via the 'link' parameter. This can allow unauthenticated attackers to make web requests to arbitrary locations originating from the web application and can be used to query and modify information from internal services."}, {"lang": "es", "value": "El complemento Dropbox Folder Share para WordPress es vulnerable a Server-Side Request Forgery en versiones hasta la 1.9.7 inclusive a través del parámetro 'link'. Esto puede permitir a atacantes no autenticados realizar solicitudes web a ubicaciones arbitrarias que se originan en la aplicación web y puede usarse para consultar y modificar información de servicios internos."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/dropbox-folder-share/trunk/HynoTech/DropboxFolderShare/Principal.php#L118", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/d62bd2bd-db01-479f-89e4-8031d69a912f?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}