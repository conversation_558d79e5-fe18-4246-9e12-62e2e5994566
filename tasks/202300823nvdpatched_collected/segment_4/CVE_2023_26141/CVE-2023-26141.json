{"cve_id": "CVE-2023-26141", "published_date": "2023-09-14T05:15:11.363", "last_modified_date": "2024-11-21T07:50:51.770", "descriptions": [{"lang": "en", "value": "Versions of the package sidekiq before 7.1.3 are vulnerable to Denial of Service (DoS) due to insufficient checks in the dashboard-charts.js file. An attacker can exploit this vulnerability by manipulating the localStorage value which will cause excessive polling requests."}, {"lang": "es", "value": "Las versiones del paquete sidekiq anteriores a la 7.1.3 son vulnerables a la Denegación de Servicio (DoS) debido a comprobaciones insuficientes en el archivo dashboard-charts.js. Un atacante puede aprovechar esta vulnerabilidad manipulando el valor de localStorage, lo que provocará peticiones excesivas."}], "references": [{"url": "https://gist.github.com/keeganparr1/1dffd3c017339b7ed5371ed3d81e6b2a", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://github.com/sidekiq/sidekiq/blob/6-x/web/assets/javascripts/dashboard.js%23L6", "source": "<EMAIL>", "tags": ["Broken Link"]}, {"url": "https://github.com/sidekiq/sidekiq/commit/62c90d7c5a7d8a378d79909859d87c2e0702bf89", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://security.snyk.io/vuln/SNYK-RUBY-SIDEKIQ-5885107", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}