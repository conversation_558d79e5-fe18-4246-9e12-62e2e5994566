{"cve_id": "CVE-2023-40221", "published_date": "2023-09-18T20:15:09.907", "last_modified_date": "2024-11-21T08:19:01.930", "descriptions": [{"lang": "en", "value": "\n\n\n\n\n\n\n\n\n\n\n\n\nThe absence of filters when loading some sections in the web application of the vulnerable device allows potential attackers to inject malicious code that will be interpreted when a legitimate user accesses the web section (MAIL SERVER) where the information is displayed. Injection can be done on parameter MAIL_RCV. When a legitimate user attempts to review NOTIFICATION/MAIL SERVER, the injected code will be executed.\n\n\n\n\n\n\n\n\n\n\n\n\n\n"}, {"lang": "es", "value": "** NO COMPATIBLE CUANDO ESTÁ ASIGNADO *** La ausencia de filtros al cargar algunas secciones en la aplicación web del dispositivo vulnerable permite a los posibles atacantes inyectar código malicioso que se interpretará cuando un usuario legítimo acceda a la sección web (SERVIDOR DE CORREO) donde se muestra la información. La inyección se puede realizar en el parámetro MAIL_RCV. Cuando un usuario legítimo intenta revisar NOTIFICACIÓN/SERVIDOR DE CORREO, se ejecutará el código inyectado. "}], "references": [{"url": "https://www.cisa.gov/news-events/ics-advisories/icsa-23-250-03", "source": "<EMAIL>", "tags": ["Third Party Advisory", "US Government Resource"]}]}