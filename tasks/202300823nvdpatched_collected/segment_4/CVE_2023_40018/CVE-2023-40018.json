{"cve_id": "CVE-2023-40018", "published_date": "2023-09-15T20:15:09.447", "last_modified_date": "2024-11-21T08:18:31.417", "descriptions": [{"lang": "en", "value": "FreeSWITCH is a Software Defined Telecom Stack enabling the digital transformation from proprietary telecom switches to a software implementation that runs on any commodity hardware. Prior to version 1.10.10, FreeSWITCH allows remote users to trigger out of bounds write by offering an ICE candidate with unknown component ID. When an SDP is offered with any ICE candidates with an unknown component ID, FreeSWITCH will make an out of bounds write to its  arrays. By abusing this vulnerability, an attacker is able to corrupt FreeSWITCH memory leading to an undefined behavior of the system or a crash of it. Version 1.10.10 contains a patch for this issue."}, {"lang": "es", "value": "FreeSWITCH es una pila de telecomunicaciones definida por software que permite la transformación digital de switches de telecomunicaciones propietarios a una implementación de software que se ejecuta en cualquier hardware básico. Antes de la versión 1.10.10, FreeSWITCH permitía a los usuarios remotos activar escritura fuera de límites ofreciendo un candidato ICE con un ID de componente desconocido. Cuando se ofrece un SDP con candidatos ICE con un ID de componente desconocido, FreeSWITCH realizará una escritura fuera de límites en sus matrices. Al abusar de esta vulnerabilidad, un atacante puede corromper la memoria de FreeSWITCH, lo que provoca un comportamiento indefinido del sistema o un bloqueo del mismo. La versión 1.10.10 contiene un parche para este problema."}], "references": [{"url": "https://github.com/signalwire/freeswitch/releases/tag/v1.10.10", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://github.com/signalwire/freeswitch/security/advisories/GHSA-7mwp-86fv-hcg3", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}