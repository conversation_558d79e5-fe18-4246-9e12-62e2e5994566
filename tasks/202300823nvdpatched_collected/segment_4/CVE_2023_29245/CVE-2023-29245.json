{"cve_id": "CVE-2023-29245", "published_date": "2023-09-19T11:16:18.100", "last_modified_date": "2024-11-21T07:56:45.230", "descriptions": [{"lang": "en", "value": "A SQL Injection vulnerability in Nozomi Networks Guardian and CMC, due to improper input validation in certain fields used in the Asset Intelligence functionality of our IDS, may allow an unauthenticated attacker to execute arbitrary SQL statements on the DBMS used by the web application by sending specially crafted malicious network packets.\n\nMalicious users with extensive knowledge on the underlying system may be able to extract arbitrary information from the DBMS in an uncontrolled way, alter its structure and data, and/or affect its availability."}, {"lang": "es", "value": "Una vulnerabilidad de inyección SQL en Nozomi Networks Guardian y CMC, debido a una validación de entrada incorrecta en ciertos campos utilizados en la funcionalidad Asset Intelligence de nuestro IDS, puede permitir que un atacante no autenticado ejecute sentencias SQL arbitrarias en el DBMS utilizado por la aplicación web mediante el envío especial paquetes de red maliciosos manipulados. Los usuarios maliciosos con amplios conocimientos sobre el sistema subyacente pueden extraer información arbitraria del DBMS de forma incontrolada o alterar su estructura y datos."}], "references": [{"url": "https://security.nozominetworks.com/NN-2023:11-01", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}