{"cve_id": "CVE-2023-25585", "published_date": "2023-09-14T21:15:10.147", "last_modified_date": "2024-11-21T07:49:46.607", "descriptions": [{"lang": "en", "value": "A flaw was found in Binutils. The use of an uninitialized field in the struct module *module may lead to application crash and local denial of service."}, {"lang": "es", "value": "Se encontró una falla en Binutils. El uso de un campo no inicializado en el módulo de estructura *module puede provocar el bloqueo de la aplicación y la denegación de servicio local."}], "references": [{"url": "https://access.redhat.com/security/cve/CVE-2023-25585", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2167498", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Patch"]}, {"url": "https://security.netapp.com/advisory/ntap-20231103-0003/", "source": "<EMAIL>", "tags": []}, {"url": "https://sourceware.org/bugzilla/show_bug.cgi?id=29892", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Patch"]}, {"url": "https://sourceware.org/git/gitweb.cgi?p=binutils-gdb.git;h=65cf035b8dc1df5d8020e0b1449514a3c42933e7", "source": "<EMAIL>", "tags": ["Mailing List", "Patch"]}]}