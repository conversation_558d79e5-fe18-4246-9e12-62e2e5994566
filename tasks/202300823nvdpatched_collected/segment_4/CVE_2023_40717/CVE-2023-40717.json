{"cve_id": "CVE-2023-40717", "published_date": "2023-09-13T13:15:09.507", "last_modified_date": "2024-11-21T08:20:01.307", "descriptions": [{"lang": "en", "value": "A use of hard-coded credentials vulnerability [CWE-798] in FortiTester 2.3.0 through 7.2.3 may allow an attacker who managed to get a shell on the device to access the database via shell commands."}, {"lang": "es", "value": "Un uso de la vulnerabilidad de credenciales codificadas [CWE-798] en FortiTester 2.3.0 a 7.2.3 puede permitir que un atacante que logró obtener un shell en el dispositivo acceda a la base de datos mediante comandos de shell."}], "references": [{"url": "https://fortiguard.com/psirt/FG-IR-22-245", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}