{"cve_id": "CVE-2023-38039", "published_date": "2023-09-15T04:15:10.127", "last_modified_date": "2024-11-21T08:12:43.457", "descriptions": [{"lang": "en", "value": "When curl retrieves an HTTP response, it stores the incoming headers so that\nthey can be accessed later via the libcurl headers API.\n\nHowever, curl did not have a limit in how many or how large headers it would\naccept in a response, allowing a malicious server to stream an endless series\nof headers and eventually cause curl to run out of heap memory."}, {"lang": "es", "value": "Cuando curl recupera una respuesta HTTP, almacena los encabezados entrantes para que se pueda acceder a ellos más tarde a través de la API de encabezados libcurl. Sin embargo, curl no tenía un límite en cuanto a la cantidad o el tamaño de encabezados que aceptaría en una respuesta, lo que permitía que un servidor malicioso transmitiera una serie interminable de encabezados y, finalmente, provocara que curl se quedara sin memoria dinámica."}], "references": [{"url": "http://seclists.org/fulldisclosure/2023/Oct/17", "source": "<EMAIL>", "tags": ["Mailing List", "Third Party Advisory"]}, {"url": "http://seclists.org/fulldisclosure/2024/Jan/34", "source": "<EMAIL>", "tags": ["Mailing List", "Third Party Advisory"]}, {"url": "http://seclists.org/fulldisclosure/2024/Jan/37", "source": "<EMAIL>", "tags": ["Mailing List", "Third Party Advisory"]}, {"url": "http://seclists.org/fulldisclosure/2024/Jan/38", "source": "<EMAIL>", "tags": ["Mailing List", "Third Party Advisory"]}, {"url": "https://hackerone.com/reports/2072338", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Patch", "Third Party Advisory"]}, {"url": "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/5DCZMYODALBLVOXVJEN2LF2MLANEYL4F/", "source": "<EMAIL>", "tags": ["Mailing List"]}, {"url": "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/M6KGKB2JNZVT276JYSKI6FV2VFJUGDOJ/", "source": "<EMAIL>", "tags": ["Mailing List"]}, {"url": "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/TEAWTYHC3RT6ZRS5OZRHLAIENVN6CCIS/", "source": "<EMAIL>", "tags": ["Mailing List"]}, {"url": "https://security.gentoo.org/glsa/202310-12", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://security.netapp.com/advisory/ntap-20231013-0005/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://support.apple.com/kb/HT214036", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://support.apple.com/kb/HT214057", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://support.apple.com/kb/HT214058", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://support.apple.com/kb/HT214063", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://www.insyde.com/security-pledge/SA-2023064", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}