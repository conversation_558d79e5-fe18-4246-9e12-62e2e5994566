{"cve_id": "CVE-2023-39643", "published_date": "2023-09-15T01:15:07.410", "last_modified_date": "2024-11-21T08:15:44.737", "descriptions": [{"lang": "en", "value": "Bl Modules xmlfeeds before v3.9.8 was discovered to contain a SQL injection vulnerability via the component SearchApiXml::Xmlfeeds()."}, {"lang": "es", "value": "Se descubrió que Bl Modules xmlfeeds anterior a v3.9.8 contiene una vulnerabilidad de inyección SQL a través del componente SearchApiXml::Xmlfeeds()."}], "references": [{"url": "https://addons.prestashop.com/en/data-import-export/5732-xml-feeds-pro.html", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://security.friendsofpresta.org/modules/2023/08/29/xmlfeeds.html", "source": "<EMAIL>", "tags": ["Exploit", "Patch", "Third Party Advisory"]}]}