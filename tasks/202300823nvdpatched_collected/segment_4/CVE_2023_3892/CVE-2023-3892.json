{"cve_id": "CVE-2023-3892", "published_date": "2023-09-19T15:15:52.053", "last_modified_date": "2024-11-21T08:18:18.363", "descriptions": [{"lang": "en", "value": "Improper Restriction of XML External Entity Reference vulnerability in MIM Assistant and Client DICOM RTst Loading modules allows XML Entity Linking / XML External Entities Blowup.\n\n\n\n\nIn order to take advantage of this vulnerability, an attacker must \ncraft a malicious XML document, embed this document into specific 3rd \nparty private RTst metadata tags, transfer the now compromised \nDICOM object to MIM, and force MIM to archive and load the data.\n\nUsers on either version are strongly encouraged to update to an unaffected version (7.2.11+, 7.3.4+).\n\nThis issue was found and analyzed by MIM Software's internal security team.  We are unaware of any proof of concept or actual exploit available in the wild.\n\n\nFor more information, visit  https://www.mimsoftware.com/cve-2023-3892 https://www.mimsoftware.com/cve-2023-3892 \n\n\n\n\nThis issue affects MIM Assistant: 7.2.10, 7.3.3; MIM Client: 7.2.10, 7.3.3.\n\n\n"}, {"lang": "es", "value": "Vulnerabilidad de Restricción Inadecuada en XML External Entity Reference en los módulos MIM Assistant and Client DICOM RTst Loading que permiten XML Entity Linking / XML External Entities Blowup. Para aprovechar esta vulnerabilidad, un atacante debe crear un documento XML malicioso, incrustar este documento en etiquetas de metadatos RTst privadas de terceros específicas, transferir el objeto DICOM ahora comprometido a MIM y obligar a MIM a archivar y cargar los datos. Se recomienda encarecidamente a los usuarios de cualquiera de las versiones que actualicen a una versión no afectada (7.2.11+, 7.3.4+). Este problema fue encontrado y analizado por el equipo de seguridad interna de MIM Software. No conocemos ninguna prueba de concepto o exploit real disponible en el mercado. Para obtener más información, visite https://www.mimsoftware.com/cve-2023-3892 https://www.mimsoftware.com/cve-2023-3892 Este problema afecta a MIM Assistant: 7.2.10, 7.3.3; Cliente MIM: 7.2.10, 7.3.3."}], "references": [{"url": "https://www.mimsoftware.com/cve-2023-3892", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}