{"cve_id": "CVE-2023-25586", "published_date": "2023-09-14T21:15:10.240", "last_modified_date": "2024-11-21T07:49:46.750", "descriptions": [{"lang": "en", "value": "A flaw was found in Binutils. A logic fail in the bfd_init_section_decompress_status function may lead to the use of an uninitialized variable that can cause a crash and local denial of service."}, {"lang": "es", "value": "Se encontró una falla en Binutils. Un error lógico en la función bfd_init_section_decompress_status puede provocar el uso de una variable no inicializada que puede provocar un bloqueo y una denegación de servicio local."}], "references": [{"url": "https://access.redhat.com/security/cve/CVE-2023-25586", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2167502", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Patch"]}, {"url": "https://security.netapp.com/advisory/ntap-20231103-0003/", "source": "<EMAIL>", "tags": []}, {"url": "https://sourceware.org/bugzilla/show_bug.cgi?id=29855", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Patch"]}, {"url": "https://sourceware.org/git/gitweb.cgi?p=binutils-gdb.git;h=5830876a0cca17bef3b2d54908928e72cca53502", "source": "<EMAIL>", "tags": ["Mailing List", "Patch"]}]}