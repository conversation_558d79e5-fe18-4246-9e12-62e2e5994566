{"cve_id": "CVE-2023-37459", "published_date": "2023-09-15T20:15:08.650", "last_modified_date": "2024-11-21T08:11:44.987", "descriptions": [{"lang": "en", "value": "Contiki-NG is an operating system for internet-of-things devices. In versions 4.9 and prior, when a packet is received, the Contiki-NG network stack attempts to start the periodic TCP timer if it is a TCP packet with the SYN flag set. But the implementation does not first verify that a full TCP header has been received. Specifically, the implementation attempts to access the flags field from the TCP buffer in the following conditional expression in the `check_for_tcp_syn` function. For this reason, an attacker can inject a truncated TCP packet, which will lead to an out-of-bound read from the packet buffer. As of time of publication, a patched version is not available. As a workaround, one can apply the changes in Contiki-NG pull request #2510 to patch the system."}, {"lang": "es", "value": "Contiki-NG es un sistema operativo para dispositivos de Internet de las cosas. En las versiones 4.9 y anteriores, cuando se recibe un paquete, la pila de red Contiki-NG intenta iniciar el temporizador TCP periódico si se trata de un paquete TCP con el indicador SYN configurado. Pero la implementación no verifica primero que se haya recibido un encabezado TCP completo. Específicamente, la implementación intenta acceder al campo de banderas desde el búfer TCP en la siguiente expresión condicional en la función `check_for_tcp_syn`. Por este motivo, un atacante puede inyectar un paquete TCP truncado, lo que provocará una lectura fuera de los límites del búfer de paquetes. Al momento de la publicación, no hay una versión parcheada disponible. Como workaround, se pueden aplicar los cambios en la solicitud de extracción #2510 de Contiki-NG para parchear el sistema."}], "references": [{"url": "https://github.com/contiki-ng/contiki-ng/pull/2510", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch"]}, {"url": "https://github.com/contiki-ng/contiki-ng/security/advisories/GHSA-6648-m23r-hq8c", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}