{"cve_id": "CVE-2023-32186", "published_date": "2023-09-19T10:15:13.357", "last_modified_date": "2024-11-21T08:02:52.080", "descriptions": [{"lang": "en", "value": "A Allocation of Resources Without Limits or Throttling vulnerability in SUSE RKE2  allows attackers with access to K3s servers apiserver/supervisor port (TCP 6443) cause denial of service.\nThis issue affects RKE2: from 1.24.0 before 1.24.17+rke2r1, from v1.25.0 before v1.25.13+rke2r1, from v1.26.0 before v1.26.8+rke2r1, from v1.27.0 before v1.27.5+rke2r1, from v1.28.0 before v1.28.1+rke2r1.\n\n"}, {"lang": "es", "value": "Una vulnerabilidad de Asignación de Recursos sin Límites o Throttling en SUSE RKE2 permite a los atacantes con acceso al puerto apiserver/supervisor de servidores K3s (TCP 6443) causar denegación de servicio. Este problema afecta a RKE2: desde la versión 1.24.0 antes de 1.24.17+rke2r1, desde la versión v1.25.0 antes de v1.25.13+rke2r1, desde la versión v1.26.0 antes de v1.26.8+rke2r1, desde la versión v1.27.0 antes de v1.27.5+rke2r1, desde la versión v1.28.0 antes de v1.28.1+rke2r1."}], "references": [{"url": "https://bugzilla.suse.com/show_bug.cgi?id=CVE-2023-32186", "source": "<EMAIL>", "tags": ["Issue Tracking", "Vendor Advisory"]}, {"url": "https://github.com/rancher/rke2/security/advisories/GHSA-p45j-vfv5-wprq", "source": "<EMAIL>", "tags": ["Mitigation", "Vendor Advisory"]}]}