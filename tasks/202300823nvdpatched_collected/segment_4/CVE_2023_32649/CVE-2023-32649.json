{"cve_id": "CVE-2023-32649", "published_date": "2023-09-19T11:16:20.297", "last_modified_date": "2024-11-21T08:03:46.247", "descriptions": [{"lang": "en", "value": "A Denial of Service (Dos) vulnerability in Nozomi Networks Guardian and CMC, due to improper input validation in certain fields used in the Asset Intelligence functionality of our IDS, allows an unauthenticated attacker to crash the IDS module by sending specially crafted malformed network packets.\n\nDuring the (limited) time window before the IDS module is automatically restarted, network traffic may not be analyzed."}, {"lang": "es", "value": "Vulnerabilidad de Denegación de Servicio (Dos) en Nozomi Networks Guardian y CMC, debido a una validación de entrada incorrecta en ciertos campos utilizados en la funcionalidad de inteligencia de activos de nuestro IDS, permite a un atacante no autenticado bloquear el módulo IDS enviando paquetes de red con formato incorrecto especialmente manipulado. Durante el período de tiempo (limitado) antes de que el módulo IDS se reinicie automáticamente, es posible que no se analice el tráfico de red. "}], "references": [{"url": "https://security.nozominetworks.com/NN-2023:10-01", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}