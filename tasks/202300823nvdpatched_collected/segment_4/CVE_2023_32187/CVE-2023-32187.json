{"cve_id": "CVE-2023-32187", "published_date": "2023-09-18T13:15:08.190", "last_modified_date": "2024-11-21T08:02:52.210", "descriptions": [{"lang": "en", "value": "An Allocation of Resources Without Limits or Throttling vulnerability in SUSE k3s allows attackers with access to K3s servers' apiserver/supervisor port (TCP 6443) cause denial of service.\nThis issue affects k3s: from v1.24.0 before v1.24.17+k3s1, from v1.25.0 before v1.25.13+k3s1, from v1.26.0 before v1.26.8+k3s1, from sev1.27.0 before v1.27.5+k3s1, from v1.28.0 before v1.28.1+k3s1.\n\n"}, {"lang": "es", "value": "Una vulnerabilidad de asignación de recursos sin límites o limitación en SUSE k3s permite a atacantes con acceso al puerto apiserver/supervisor de los servidores K3s (TCP 6443) provocar denegación de servicio. Este problema afecta a k3s: desde v1.24.0 antes de v1.24.17+k3s1, desde v1.25.0 antes de v1.25.13+k3s1, desde v1.26.0 antes de v1.26.8+k3s1, desde sev1.27.0 antes de v1.27.5+k3s1, desde v1.28.0 antes de v1.28.1+k3s1"}], "references": [{"url": "https://bugzilla.suse.com/show_bug.cgi?id=CVE-2023-32187https://", "source": "<EMAIL>", "tags": ["Broken Link"]}, {"url": "https://github.com/k3s-io/k3s/security/advisories/GHSA-m4hf-6vgr-75r2", "source": "<EMAIL>", "tags": ["Mitigation", "Vendor Advisory"]}]}