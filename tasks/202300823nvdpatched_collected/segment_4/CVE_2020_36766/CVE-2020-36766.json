{"cve_id": "CVE-2020-36766", "published_date": "2023-09-18T09:15:07.693", "last_modified_date": "2024-11-21T05:30:15.533", "descriptions": [{"lang": "en", "value": "An issue was discovered in the Linux kernel before 5.8.6. drivers/media/cec/core/cec-api.c leaks one byte of kernel memory on specific hardware to unprivileged users, because of directly assigning log_addrs with a hole in the struct."}, {"lang": "es", "value": "Se descubrió un problema en el kernel de Linux anterior a 5.8.6. drivers/media/cec/core/cec-api.c pierde un byte de memoria del kernel en hardware específico a usuarios sin privilegios, debido a la asignación directa de log_addrs con un agujero en la estructura."}], "references": [{"url": "https://cdn.kernel.org/pub/linux/kernel/v5.x/ChangeLog-5.8.6", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://github.com/torvalds/linux/commit/6c42227c3467549ddc65efe99c869021d2f4a570", "source": "<EMAIL>", "tags": ["Patch"]}]}