{"cve_id": "CVE-2023-20135", "published_date": "2023-09-13T17:15:09.253", "last_modified_date": "2024-11-21T07:40:38.240", "descriptions": [{"lang": "en", "value": "A vulnerability in Cisco IOS XR Software image verification checks could allow an authenticated, local attacker to execute arbitrary code on the underlying operating system.\r\n\r This vulnerability is due to a time-of-check, time-of-use (TOCTOU) race condition when an install query regarding an ISO image is performed during an install operation that uses an ISO image. An attacker could exploit this vulnerability by modifying an ISO image and then carrying out install requests in parallel. A successful exploit could allow the attacker to execute arbitrary code on an affected device."}, {"lang": "es", "value": "Una vulnerabilidad en las comprobaciones de verificación de imágenes del Software Cisco IOS XR podría permitir que un atacante local autenticado ejecute código arbitrario en el sistema operativo subyacente. Esta vulnerabilidad se debe a una condición de ejecución de tiempo de verificación, tiempo de uso (TOCTOU) cuando se realiza una consulta de instalación relacionada con una imagen ISO durante una operación de instalación que utiliza una imagen ISO. Un atacante podría aprovechar esta vulnerabilidad modificando una imagen ISO y luego realizando solicitudes de instalación en paralelo. Una explotación existosa podría permitir al atacante ejecutar código arbitrario en un dispositivo afectado."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-lnt-L9zOkBz5", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}