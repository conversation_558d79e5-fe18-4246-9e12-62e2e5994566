{"cve_id": "CVE-2023-36472", "published_date": "2023-09-15T19:15:08.117", "last_modified_date": "2024-11-21T08:09:46.987", "descriptions": [{"lang": "en", "value": "Strapi is an open-source headless content management system. Prior to version 4.11.7, an unauthorized actor can get access to user reset password tokens if they have the configure view permissions. The `/content-manager/relations` route does not remove private fields or ensure that they can't be selected. This issue is fixed in version 4.11.7."}, {"lang": "es", "value": "Strapi es un sistema de gestión de contenidos headless de código abierto. Antes de la versión 4.11.7, un actor no autorizado puede obtener acceso a los tokens de restablecimiento de contraseña del usuario si tiene permisos de visualización de la configuración. La ruta `/content-manager/relations` no elimina campos privados ni garantiza que no se puedan seleccionar. Este problema se solucionó en la versión 4.11.7."}], "references": [{"url": "https://github.com/strapi/strapi/releases/tag/v4.11.7", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://github.com/strapi/strapi/security/advisories/GHSA-v8gg-4mq2-88q4", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}