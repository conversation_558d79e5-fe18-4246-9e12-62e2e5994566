{"cve_id": "CVE-2023-22513", "published_date": "2023-09-19T17:15:08.017", "last_modified_date": "2025-03-06T16:15:40.103", "descriptions": [{"lang": "en", "value": "This High severity RCE (Remote Code Execution) vulnerability was introduced in version 8.0.0 of Bitbucket Data Center and Server. This RCE (Remote Code Execution) vulnerability, with a CVSS Score of 8.5, allows an authenticated attacker to execute arbitrary code which has high impact to confidentiality, high impact to integrity, high impact to availability, and requires no user interaction. <PERSON><PERSON> recommends that Bitbucket Data Center and Server customers upgrade to latest version, if you are unable to do so, upgrade your instance to one of the specified supported fixed versions: Bitbucket Data Center and Server 8.9: Upgrade to a release greater than or equal to 8.9.5 Bitbucket Data Center and Server 8.10: Upgrade to a release greater than or equal to 8.10.5 Bitbucket Data Center and Server 8.11: Upgrade to a release greater than or equal to 8.11.4 Bitbucket Data Center and Server 8.12: Upgrade to a release greater than or equal to 8.12.2 Bitbucket Data Center and Server 8.13: Upgrade to a release greater than or equal to 8.13.1 Bitbucket Data Center and Server 8.14: Upgrade to a release greater than or equal to 8.14.0 Bitbucket Data Center and Server version >= 8.0 and < 8.9: Upgrade to any of the listed fix versions. See the release notes (https://confluence.atlassian.com/bitbucketserver/release-notes). You can download the latest version of Bitbucket Data Center and Server from the download center (https://www.atlassian.com/software/bitbucket/download-archives). This vulnerability was discovered by a private user and reported via our Bug Bounty program"}, {"lang": "es", "value": "Esta vulnerabilidad RCE (Ejecución Remota de Código) de alta gravedad se introdujo en la versión 8.0.0 de Bitbucket Data Center and Server. Esta vulnerabilidad RCE (Ejecución Remota de Código), con una puntuación CVSS de 8,5, permite a un atacante autenticado ejecutar código arbitrario que tiene un alto impacto en la confidencialidad, un alto impacto en la integridad, un alto impacto en la disponibilidad y no requiere interacción del usuario. Atlassian recomienda que los clientes de Bitbucket Data Center y Server actualicen a la última versión; si no puede hacerlo, actualice su instancia a una de las versiones fijas admitidas especificadas: Bitbucket Data Center y Server 8.9: actualice a una versión superior o igual a 8.9.5 Bitbucket Data Center y Server 8.10: actualice a una versión mayor o igual a 8.10.5 Bitbucket Data Center y Server 8.11: actualice a una versión mayor o igual a 8.11.4 Bitbucket Data Center y Server 8.12: actualice a una versión mayor o igual a 8.12.2 Bitbucket Data Center y Server 8.13: actualice a una versión mayor o igual a 8.13.1 Bitbucket Data Center y Server 8.14: actualice a una versión mayor o igual a 8.14.0 Bitbucket Data Versión de Center y Server &gt;= 8.0 y &lt; 8.9: actualice a cualquiera de las versiones de corrección enumeradas. Consulte las notas de la versión (https://confluence.atlassian.com/bitbucketserver/release-notes). Puede descargar la última versión de Bitbucket Data Center and Server desde el centro de descargas (https://www.atlassian.com/software/bitbucket/download-archives). Esta vulnerabilidad fue descubierta por un usuario privado y reportada a través de nuestro programa Bug Bounty."}], "references": [{"url": "https://confluence.atlassian.com/pages/viewpage.action?pageId=1283691616", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://jira.atlassian.com/browse/BSERV-14419", "source": "<EMAIL>", "tags": ["Issue Tracking", "Vendor Advisory"]}]}