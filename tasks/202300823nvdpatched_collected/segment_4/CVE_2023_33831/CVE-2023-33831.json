{"cve_id": "CVE-2023-33831", "published_date": "2023-09-18T20:15:09.377", "last_modified_date": "2024-11-21T08:06:02.613", "descriptions": [{"lang": "en", "value": "A remote command execution (RCE) vulnerability in the /api/runscript endpoint of FUXA 1.1.13 allows attackers to execute arbitrary commands via a crafted POST request."}, {"lang": "es", "value": "Una vulnerabilidad de ejecución remota de comandos (RCE) en el endpoint /api/runscript de FUXA 1.1.13 permite a los atacantes ejecutar comandos arbitrarios a través de una solicitud POST manipulada."}], "references": [{"url": "https://github.com/rodolfomarianocy/Unauthenticated-RCE-FUXA-CVE-2023-33831", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://youtu.be/Xxa6yRB2Fpw", "source": "<EMAIL>", "tags": ["Exploit"]}]}