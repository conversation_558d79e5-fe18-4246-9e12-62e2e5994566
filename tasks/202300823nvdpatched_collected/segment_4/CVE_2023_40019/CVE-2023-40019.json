{"cve_id": "CVE-2023-40019", "published_date": "2023-09-15T20:15:09.637", "last_modified_date": "2024-11-21T08:18:31.530", "descriptions": [{"lang": "en", "value": "FreeSWITCH is a Software Defined Telecom Stack enabling the digital transformation from proprietary telecom switches to a software implementation that runs on any commodity hardware. Prior to version 1.10.10, FreeSWITCH allows authorized users to cause a denial of service attack by sending re-INVITE with SDP containing duplicate codec names. When a call in FreeSWITCH completes codec negotiation, the `codec_string` channel variable is set with the result of the negotiation. On a subsequent re-negotiation, if an SDP is offered that contains codecs with the same names but with different formats, there may be too many codec matches detected by FreeSWITCH leading to overflows of its internal arrays. By abusing this vulnerability, an attacker is able to corrupt stack of FreeSWITCH leading to an undefined behavior of the system or simply crash it. Version 1.10.10 contains a patch for this issue."}, {"lang": "es", "value": "FreeSWITCH es una pila de telecomunicaciones definida por software que permite la transformación digital de switches de telecomunicaciones propietarios a una implementación de software que se ejecuta en cualquier hardware básico. Antes de la versión 1.10.10, FreeSWITCH permitía a los usuarios autorizados provocar un ataque de denegación de servicio enviando un nuevo INVITE con SDP que contenía nombres de códec duplicados. Cuando una llamada en FreeSWITCH completa la negociación del códec, la variable de canal `codec_string` se configura con el resultado de la negociación. En una renegociación posterior, si se ofrece un SDP que contiene códecs con los mismos nombres pero con diferentes formatos, es posible que FreeSWITCH detecte demasiadas coincidencias de códecs, lo que provocará desbordamientos de sus matrices internas. Al abusar de esta vulnerabilidad, un atacante puede corromper la pila de FreeSWITCH, lo que provoca un comportamiento indefinido del sistema o simplemente bloquearlo. La versión 1.10.10 contiene un parche para este problema."}], "references": [{"url": "https://github.com/signalwire/freeswitch/releases/tag/v1.10.10", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://github.com/signalwire/freeswitch/security/advisories/GHSA-gjj5-79p2-9g3q", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}