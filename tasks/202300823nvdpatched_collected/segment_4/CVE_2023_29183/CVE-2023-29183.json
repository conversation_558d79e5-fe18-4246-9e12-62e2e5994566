{"cve_id": "CVE-2023-29183", "published_date": "2023-09-13T13:15:08.367", "last_modified_date": "2024-11-21T07:56:40.483", "descriptions": [{"lang": "en", "value": "An improper neutralization of input during web page generation ('Cross-site Scripting') vulnerability [CWE-79] in FortiProxy 7.2.0 through 7.2.4, 7.0.0 through 7.0.10 and FortiOS 7.2.0 through 7.2.4, 7.0.0 through 7.0.11, 6.4.0 through 6.4.12, 6.2.0 through 6.2.14 GUI may allow an authenticated attacker to trigger malicious JavaScript code execution via crafted guest management setting."}, {"lang": "es", "value": "Una neutralización inadecuada de la entrada durante la vulnerabilidad de generación de páginas web ('Cross-Site Scripting') [CWE-79] en FortiProxy 7.2.0 a 7.2.4, 7.0.0 a 7.0.10 y FortiOS 7.2.0 a 7.2.4, Las versiones 7.0.0 a 7.0.11, 6.4.0 a 6.4.12, 6.2.0 a 6.2.14 pueden permitir que un atacante autenticado desencadene la ejecución de código JavaScript malicioso a través de una configuración de administración de invitados manipulados."}], "references": [{"url": "https://fortiguard.com/psirt/FG-IR-23-106", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}