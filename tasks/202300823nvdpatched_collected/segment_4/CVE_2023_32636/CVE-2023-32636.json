{"cve_id": "CVE-2023-32636", "published_date": "2023-09-14T20:15:09.653", "last_modified_date": "2024-11-21T08:03:44.800", "descriptions": [{"lang": "en", "value": "A flaw was found in glib, where the gvariant deserialization code is vulnerable to a denial of service introduced by additional input validation added to resolve CVE-2023-29499. The offset table validation may be very slow. This bug does not affect any released version of glib but does affect glib distributors who followed the guidance of glib developers to backport the initial fix for CVE-2023-29499."}, {"lang": "es", "value": "Se encontró una falla en glib, donde el código de deserialización gvariant es vulnerable a una denegación de servicio introducida por una validación de entrada adicional agregada para resolver CVE-2023-29499. La validación de la tabla de desplazamiento puede ser muy lenta. Este error no afecta a ninguna versión publicada de glib, pero sí afecta a los distribuidores de glib que siguieron las instrucciones de los desarrolladores de glib para respaldar la solución inicial para CVE-2023-29499."}], "references": [{"url": "https://gitlab.gnome.org/GNOME/glib/-/issues/2841", "source": "<EMAIL>", "tags": ["Issue Tracking", "Vendor Advisory"]}, {"url": "https://https://discourse.gnome.org/t/multiple-fixes-for-gvariant-normalisation-issues-in-glib/12835", "source": "<EMAIL>", "tags": ["Broken Link"]}, {"url": "https://security.netapp.com/advisory/ntap-*************/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}