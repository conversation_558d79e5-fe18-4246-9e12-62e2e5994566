{"cve_id": "CVE-2023-36638", "published_date": "2023-09-13T13:15:09.033", "last_modified_date": "2024-11-21T08:10:08.903", "descriptions": [{"lang": "en", "value": "An improper privilege management vulnerability [CWE-269] in FortiManager 7.2.0 through 7.2.2, 7.0.0 through 7.0.7, 6.4.0 through 6.4.11, 6.2 all versions, 6.0 all versions and FortiAnalyzer 7.2.0 through 7.2.2, 7.0.0 through 7.0.7, 6.4.0 through 6.4.11, 6.2 all versions, 6.0 all versions API may allow a remote and authenticated API admin user to access some system settings such as the mail server settings through the API via a stolen GUI session ID."}, {"lang": "es", "value": "Una vulnerabilidad de administración de privilegios inadecuada [CWE-269] en FortiManager 7.2.0 a 7.2.2, 7.0.0 a 7.0.7, 6.4.0 a 6.4.11, 6.2 todas las versiones, 6.0 todas las versiones y FortiAnalyzer 7.2.0 a 7.2 .2, 7.0.0 a 7.0.7, 6.4.0 a 6.4.11, 6.2 todas las versiones, 6.0 todas las versiones La API puede permitir que un usuario administrador de API remoto y autenticado acceda a algunas configuraciones del sistema, como la configuración del servidor de correo a través de la API a través de una ID de sesión de GUI robada."}], "references": [{"url": "https://fortiguard.com/psirt/FG-IR-22-522", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}