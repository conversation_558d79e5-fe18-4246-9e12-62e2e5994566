{"cve_id": "CVE-2023-3301", "published_date": "2023-09-13T17:15:10.063", "last_modified_date": "2024-11-21T08:16:57.723", "descriptions": [{"lang": "en", "value": "A flaw was found in QEMU. The async nature of hot-unplug enables a race scenario where the net device backend is cleared before the virtio-net pci frontend has been unplugged. A malicious guest could use this time window to trigger an assertion and cause a denial of service."}, {"lang": "es", "value": "Se encontró una falla en QEMU. La naturaleza asíncrona de la desconexión en caliente permite un escenario de ejecución en el que el backend del dispositivo de red se borra antes de que se haya desconectado el frontend pci de virtio-net. Un invitado malintencionado podría utilizar esta ventana de tiempo para desencadenar una aserción y provocar una denegación de servicio."}], "references": [{"url": "https://access.redhat.com/security/cve/CVE-2023-3301", "source": "<EMAIL>", "tags": ["Patch", "Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2215784", "source": "<EMAIL>", "tags": ["Issue Tracking", "Third Party Advisory"]}, {"url": "https://security.netapp.com/advisory/ntap-20231020-0008/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}