{"cve_id": "CVE-2022-3466", "published_date": "2023-09-15T14:15:08.037", "last_modified_date": "2024-11-21T07:19:35.063", "descriptions": [{"lang": "en", "value": "The version of cri-o as released for Red Hat OpenShift Container Platform 4.9.48, 4.10.31, and 4.11.6 via RHBA-2022:6316, RHBA-2022:6257, and RHBA-2022:6658, respectively, included an incorrect version of cri-o missing the fix for CVE-2022-27652, which was previously fixed in OCP 4.9.41 and 4.10.12 via RHBA-2022:5433 and RHSA-2022:1600. This issue could allow an attacker with access to programs with inheritable file capabilities to elevate those capabilities to the permitted set when execve(2) runs. For more details, see https://access.redhat.com/security/cve/CVE-2022-27652."}, {"lang": "es", "value": "La versión de cri-o publicada para Red Hat OpenShift Container Platform 4.9.48, 4.10.31 y 4.11.6 a través de RHBA-2022:6316, RHBA-2022:6257 y RHBA-2022:6658, respectivamente, incluía una versión incorrecta de cri-o le falta la solución para CVE-2022-27652, que se solucionó anteriormente en OCP 4.9.41 y 4.10.12 a través de RHBA-2022:5433 y RHSA-2022:1600. Este problema podría permitir que un atacante con acceso a programas con capacidades de archivos heredables eleve esas capacidades al conjunto permitido cuando se ejecuta execve(2). Para obtener más detalles, consulte https://access.redhat.com/security/cve/CVE-2022-27652."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2022:7398", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/security/cve/CVE-2022-3466", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2134063", "source": "<EMAIL>", "tags": ["Issue Tracking", "Vendor Advisory"]}]}