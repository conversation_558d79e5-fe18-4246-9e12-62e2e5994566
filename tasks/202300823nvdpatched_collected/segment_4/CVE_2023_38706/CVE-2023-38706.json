{"cve_id": "CVE-2023-38706", "published_date": "2023-09-15T20:15:09.217", "last_modified_date": "2024-11-21T08:14:05.653", "descriptions": [{"lang": "en", "value": "Discourse is an open-source discussion platform. Prior to version 3.1.1 of the `stable` branch and version 3.2.0.beta1 of the `beta` and `tests-passed` branches, a malicious user can create an unlimited number of drafts with very long draft keys which may end up exhausting the resources on the server. The issue is patched in version 3.1.1 of the `stable` branch and version 3.2.0.beta1 of the `beta` and `tests-passed` branches. There are no known workarounds."}, {"lang": "es", "value": "Discourse es una plataforma de debate de código abierto. Antes de la versión 3.1.1 de la rama `stable` y la versión 3.2.0.beta1 de las ramas `beta` y `tests-passed`, un usuario malintencionado podía crear un número ilimitado de borradores con claves de borrador muy largas que podían finalizar agotar los recursos del servidor. El problema se solucionó en la versión 3.1.1 de la rama \"estable\" y en la versión 3.2.0.beta1 de las ramas \"beta\" y \"tests-passed\". No se conocen workarounds."}], "references": [{"url": "https://github.com/discourse/discourse/security/advisories/GHSA-7wpp-4pqg-gvp8", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}