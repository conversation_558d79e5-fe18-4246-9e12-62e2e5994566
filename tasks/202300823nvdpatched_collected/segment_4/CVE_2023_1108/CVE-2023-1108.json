{"cve_id": "CVE-2023-1108", "published_date": "2023-09-14T15:15:08.293", "last_modified_date": "2024-11-21T07:38:28.330", "descriptions": [{"lang": "en", "value": "A flaw was found in undertow. This issue makes achieving a denial of service possible due to an unexpected handshake status updated in SslConduit, where the loop never terminates."}, {"lang": "es", "value": "Se encontró una falla en undertow. Este problema hace posible lograr una denegación de servicio debido a un estado de protocolo de enlace inesperado actualizado en SslConduit, donde el bucle nunca termina"}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2023:1184", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:1185", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:1512", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:1513", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:1514", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:1516", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:2135", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/errata/RHSA-2023:3883", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:3884", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:3885", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:3888", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:3892", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:3954", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/errata/RHSA-2023:4612", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/security/cve/CVE-2023-1108", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2174246", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://github.com/advisories/GHSA-m4mm-pg93-fv78", "source": "<EMAIL>", "tags": []}, {"url": "https://security.netapp.com/advisory/ntap-20231020-0002/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}