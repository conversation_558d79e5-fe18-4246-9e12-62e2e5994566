{"cve_id": "CVE-2023-34195", "published_date": "2023-09-18T13:15:08.487", "last_modified_date": "2024-11-21T08:06:45.087", "descriptions": [{"lang": "en", "value": "An issue was discovered in SystemFirmwareManagementRuntimeDxe in Insyde InsydeH2O with kernel 5.0 through 5.5. The implementation of the GetImage method retrieves the value of a runtime variable named GetImageProgress, and later uses this value as a function pointer. This variable is wiped out by the same module near the end of the function. By setting this UEFI variable from the OS to point into custom code, an attacker could achieve arbitrary code execution in the DXE phase, before several chipset locks are set."}, {"lang": "es", "value": "Se descubrió un problema en SystemFirmwareManagementRuntimeDxe en Insyde InsydeH2O con kernel 5.0 a 5.5. La implementación del método GetImage recupera el valor de una variable de tiempo de ejecución denominada GetImageProgress y luego usa este valor como puntero de función. Esta variable es eliminada por el mismo módulo cerca del final de la función. Al configurar esta variable UEFI desde el sistema operativo para que apunte a un código personalizado, un atacante podría lograr la ejecución de código arbitrario en la fase DXE, antes de que se establezcan varios bloqueos de chipset."}], "references": [{"url": "https://www.insyde.com/security-pledge", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://www.insyde.com/security-pledge/SA-2023052", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}