{"cve_id": "CVE-2021-26837", "published_date": "2023-09-19T00:15:33.093", "last_modified_date": "2024-11-21T05:56:53.253", "descriptions": [{"lang": "en", "value": "SQL Injection vulnerability in SearchTextBox parameter in Fortra (Formerly HelpSystems) DeliverNow before version 1.2.18, allows attackers to execute arbitrary code, escalate privileges, and gain sensitive information."}, {"lang": "es", "value": "La vulnerabilidad de inyección SQL en el parámetro SearchTextBox en Fortra (Formerly HelpSystems) DeliverNow antes de la versión 1.2.18, permite a los atacantes ejecutar código arbitrario, escalar privilegios y obtener información sensible."}], "references": [{"url": "https://community.helpsystems.com/knowledge-base/rjs/delivernow/overview/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://susos.co/blog/f/cve-disclosure-sedric-louissaints-discovery-of-sql-injection-in", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}