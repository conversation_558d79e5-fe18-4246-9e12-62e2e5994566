{"cve_id": "CVE-2023-37263", "published_date": "2023-09-15T19:15:08.637", "last_modified_date": "2024-11-21T08:11:20.737", "descriptions": [{"lang": "en", "value": "Strapi is the an open-source headless content management system. Prior to version 4.12.1, field level permissions are not respected in the relationship title. If an actor has relationship title and the relationship shows a field they don't have permission to see, the field will still be visible. Version 4.12.1 has a fix for this issue."}, {"lang": "es", "value": "Strapi es un sistema de gestión de contenidos headless de código abierto. Antes de la versión 4.12.1, los permisos a nivel de campo no se respetaban en el título de la relación. Si un actor tiene un título de relación y la relación muestra un campo para el que no tiene permiso para ver, el campo seguirá estando visible. La versión 4.12.1 tiene una solución para este problema."}], "references": [{"url": "https://github.com/strapi/strapi/releases/tag/v4.12.1", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://github.com/strapi/strapi/security/advisories/GHSA-m284-85mf-cgrc", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}