{"cve_id": "CVE-2023-26143", "published_date": "2023-09-19T05:17:10.443", "last_modified_date": "2024-11-21T07:50:52.047", "descriptions": [{"lang": "en", "value": "Versions of the package blamer before 1.0.4 are vulnerable to Arbitrary Argument Injection via the blameByFile() API. The library does not sanitize for user input or validate the given file path conforms to a specific schema, nor does it properly pass command-line flags to the git binary using the double-dash POSIX characters (--) to communicate the end of options."}, {"lang": "es", "value": "Las versiones del paquete blamer anteriores a 1.0.4 son vulnerables a la inyección Arbitraria de Argumentos a través de la API blameByFile(). La librería no sanitiza la entrada del usuario ni valida que la ruta de archivo dada se ajuste a un esquema específico, ni pasa correctamente los indicadores de línea de comandos al binario git utilizando los caracteres POSIX de doble guión (--) para comunicar el final de las opciones. "}], "references": [{"url": "https://gist.github.com/lirantal/14c3686370a86461f555d3f0703e02f9", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/kucherenko/blamer/commit/0965877f115753371a2570f10a63c455d2b2cde3", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://security.snyk.io/vuln/SNYK-JS-BLAMER-5731318", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}