{"cve_id": "CVE-2023-32611", "published_date": "2023-09-14T20:15:09.550", "last_modified_date": "2024-11-21T08:03:41.770", "descriptions": [{"lang": "en", "value": "A flaw was found in GLib. GVariant deserialization is vulnerable to a slowdown issue where a crafted GVariant can cause excessive processing, leading to denial of service."}, {"lang": "es", "value": "Se encontró una falla en GLib. La deserialización de GVariant es vulnerable a un problema de desaceleración en el que un GVariant manipulado puede provocar un procesamiento excesivo y provocar una denegación de servicio."}], "references": [{"url": "https://access.redhat.com/security/cve/CVE-2023-32611", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2211829", "source": "<EMAIL>", "tags": ["Issue Tracking", "Third Party Advisory"]}, {"url": "https://gitlab.gnome.org/GNOME/glib/-/issues/2797", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://lists.debian.org/debian-lts-announce/2023/09/msg00030.html", "source": "<EMAIL>", "tags": []}, {"url": "https://security.gentoo.org/glsa/202311-18", "source": "<EMAIL>", "tags": []}, {"url": "https://security.netapp.com/advisory/ntap-20231027-0005/", "source": "<EMAIL>", "tags": []}]}