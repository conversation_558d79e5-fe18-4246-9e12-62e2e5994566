{"cve_id": "CVE-2023-2567", "published_date": "2023-09-19T11:16:19.333", "last_modified_date": "2025-05-05T14:14:43.813", "descriptions": [{"lang": "en", "value": "A SQL Injection vulnerability has been found in Nozomi Networks Guardian and CMC, due to improper input validation in certain parameters used in the Query functionality.\nAuthenticated users may be able to execute arbitrary SQL statements on the DBMS used by the web application."}, {"lang": "es", "value": "Vulnerabilidad de inyección SQL en Nozomi Networks Guardian y CMC, debido a una validación de entrada incorrecta en ciertos parámetros utilizados en la funcionalidad de consulta, permite a un atacante autenticado ejecutar consultas SQL arbitrarias en el DBMS utilizado por la aplicación web. Los usuarios autenticados pueden extraer información arbitraria del DBMS de forma incontrolada. "}], "references": [{"url": "https://security.nozominetworks.com/NN-2023:9-01", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}