{"cve_id": "CVE-2023-37281", "published_date": "2023-09-15T20:15:08.310", "last_modified_date": "2024-11-21T08:11:23.297", "descriptions": [{"lang": "en", "value": "Contiki-NG is an operating system for internet-of-things devices. In versions 4.9 and prior, when processing the various IPv6 header fields during IPHC header decompression, Contiki-NG confirms the received packet buffer contains enough data as needed for that field. But no similar check is done before decompressing the IPv6 address. Therefore, up to 16 bytes can be read out of bounds on the line with the statement `memcpy(&ipaddr->u8[16 - postcount], iphc_ptr, postcount);`. The value of `postcount` depends on the address compression used in the received packet and can be controlled by the attacker. As a result, an attacker can inject a packet that causes an out-of-bound read. As of time of publication, a patched version is not available. As a workaround, one can apply the changes in Contiki-NG pull request #2509 to patch the system."}, {"lang": "es", "value": "Contiki-NG es un sistema operativo para dispositivos de Internet de las cosas. En las versiones 4.9 y anteriores, al procesar los diversos campos del encabezado IPv6 durante la descompresión del encabezado IPHC, Contiki-NG confirma que el búfer del paquete recibido contiene suficientes datos necesarios para ese campo. Pero no se realiza ninguna verificación similar antes de descomprimir la dirección IPv6. <PERSON>r lo tanto, se pueden leer hasta 16 bytes fuera de límites en la línea con la declaración `memcpy(&amp;ipaddr-&gt;u8[16 - postcount], iphc_ptr, postcount);`. El valor de \"postcount\" depende de la compresión de dirección utilizada en el paquete recibido y puede ser controlado por el atacante. Como resultado, un atacante puede inyectar un paquete que provoque una lectura fuera de los límites. En el momento de la publicación, no hay una versión parcheada disponible. Como workaround, se pueden aplicar los cambios en la solicitud de extracción #2509 de Contiki-NG para parchear el sistema."}], "references": [{"url": "https://github.com/contiki-ng/contiki-ng/pull/2509", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch"]}, {"url": "https://github.com/contiki-ng/contiki-ng/security/advisories/GHSA-2v4c-9p48-g9pr", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}