{"cve_id": "CVE-2023-20191", "published_date": "2023-09-13T17:15:09.440", "last_modified_date": "2024-11-21T07:40:47.793", "descriptions": [{"lang": "en", "value": "A vulnerability in the access control list (ACL) processing on MPLS interfaces in the ingress direction of Cisco IOS XR Software could allow an unauthenticated, remote attacker to bypass a configured ACL.\r\n\r This vulnerability is due to incomplete support for this feature. An attacker could exploit this vulnerability by attempting to send traffic through an affected device. A successful exploit could allow the attacker to bypass an ACL on the affected device.\r\n\r   There are workarounds that address this vulnerability.\r\n\r   \r\n\r \r This advisory is part of the September 2023 release of the Cisco IOS XR Software Security Advisory Bundled Publication. For a complete list of the advisories and links to them, see Cisco Event Response: September 2023 Semiannual Cisco IOS XR Software Security Advisory Bundled Publication ."}, {"lang": "es", "value": "Una vulnerabilidad en el procesamiento de la lista de control de acceso (ACL) en las interfaces MPLS en la dirección de ingreso del software Cisco IOS XR podría permitir que un atacante remoto no autenticado omita una ACL configurada. Esta vulnerabilidad se debe a la compatibilidad incompleta con esta característica. Un atacante podría aprovechar esta vulnerabilidad intentando enviar tráfico a través de un dispositivo afectado. Una explotación existosa podría permitir al atacante eludir una ACL en el dispositivo afectado. Existen workarounds que abordan esta vulnerabilidad. Este aviso es parte de la publicación de septiembre de 2023 del paquete de avisos de seguridad del software Cisco IOS XR."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-dnx-acl-PyzDkeYF", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}