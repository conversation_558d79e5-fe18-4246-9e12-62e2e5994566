{"cve_id": "CVE-2022-20917", "published_date": "2023-09-15T03:15:07.520", "last_modified_date": "2024-11-21T06:43:48.703", "descriptions": [{"lang": "en", "value": "A vulnerability in the Extensible Messaging and Presence Protocol (XMPP) message processing feature of Cisco Jabber could allow an authenticated, remote attacker to manipulate the content of XMPP messages that are used by the affected application.\r\n This vulnerability is due to the improper handling of nested XMPP messages within requests that are sent to the Cisco Jabber client software. An attacker could exploit this vulnerability by connecting to an XMPP messaging server and sending crafted XMPP messages to an affected Jabber client. A successful exploit could allow the attacker to manipulate the content of XMPP messages, possibly allowing the attacker to cause the Jabber client application to perform unsafe actions."}, {"lang": "es", "value": "Una vulnerabilidad en el Extensible Messaging y Presence Protocol (XMPP) característica del procesamiento de mensajes de Cisco Jabber podría permitir que un atacante remoto autenticado manipule el contenido de los mensajes XMPP que utiliza la aplicación afectada. Esta vulnerabilidad se debe al manejo inadecuado de mensajes XMPP anidados dentro de las solicitudes que se envían al software cliente Cisco Jabber. Un atacante podría aprovechar esta vulnerabilidad conectándose a un servidor de mensajería XMPP y enviando mensajes XMPP manipulados a un cliente Jabber afectado. Un exploit exitoso podría permitir al atacante manipular el contenido de los mensajes XMPP, lo que posiblemente le permitiría provocar que la aplicación cliente Jabber realice acciones inseguras."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-jabber-xmpp-Ne9SCM", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}