{"cve_id": "CVE-2023-39916", "published_date": "2023-09-13T15:15:07.837", "last_modified_date": "2024-11-21T08:16:02.057", "descriptions": [{"lang": "en", "value": "NLnet Labs’ Routinator 0.9.0 up to and including 0.12.1 contains a possible path traversal vulnerability in the optional, off-by-default keep-rrdp-responses feature that allows users to store the content of responses received for RRDP requests. The location of these stored responses is constructed from the URL of the request. Due to insufficient sanitation of the URL, it is possible for an attacker to craft a URL that results in the response being stored outside of the directory specified for it."}, {"lang": "es", "value": "Routinator 0.9.0 de NLnet Labs hasta 0.12.1 inclusive contiene una posible vulnerabilidad de Path Traversal en la función opcional keep-rrdp-responses, desactivada por defecto, que permite a los usuarios almacenar el contenido de las respuestas recibidas para solicitudes RRDP. La ubicación de estas respuestas almacenadas se construye a partir de la URL de la solicitud. Debido a una limpieza insuficiente de la URL, es posible que un atacante cree una URL que dé lugar a que la respuesta se almacene fuera del directorio especificado para ella."}], "references": [{"url": "https://nlnetlabs.nl/downloads/routinator/CVE-2023-39916.txt", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}