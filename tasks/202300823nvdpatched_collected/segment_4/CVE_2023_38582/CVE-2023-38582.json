{"cve_id": "CVE-2023-38582", "published_date": "2023-09-18T21:15:54.693", "last_modified_date": "2024-11-21T08:13:52.483", "descriptions": [{"lang": "en", "value": "\n\n\n\n\n\n\n\n\nPersistent cross-site scripting (XSS) in the web application of MOD3GP-SY-120K allows an authenticated remote attacker to introduce arbitrary JavaScript by injecting an XSS payload into the field MAIL_RCV. When a legitimate user attempts to access to the vulnerable page of the web application, the XSS payload will be executed.\n\n\n\n\n\n\n\n\n\n"}, {"lang": "es", "value": "** NO COMPATIBLE CUANDO ESTÁ ASIGNADO ** Cross-Site Sripting persistente (XSS) en la aplicación web de MOD3GP-SY-120K permite a un atacante remoto autenticado introducir JavaScript arbitrario inyectando un payload XSS en el campo MAIL_RCV. Cuando un usuario legítimo intenta acceder a la página vulnerable de la aplicación web, se ejecutará un payload XSS. "}], "references": [{"url": "https://www.cisa.gov/news-events/ics-advisories/icsa-23-250-03", "source": "<EMAIL>", "tags": ["Third Party Advisory", "US Government Resource"]}]}