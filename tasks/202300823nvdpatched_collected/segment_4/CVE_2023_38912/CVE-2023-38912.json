{"cve_id": "CVE-2023-38912", "published_date": "2023-09-14T21:15:10.560", "last_modified_date": "2024-11-21T08:14:26.050", "descriptions": [{"lang": "en", "value": "SQL injection vulnerability in Super Store Finder PHP Script v.3.6 allows a remote attacker to execute arbitrary code via a crafted payload to the username parameter."}, {"lang": "es", "value": "Vulnerabilidad de inyección SQL en Super Store Finder PHP Script v.3.6 permite a un atacante remoto ejecutar código arbitrario a través de un payload manipulado en el parámetro de nombre de usuario."}], "references": [{"url": "https://codecanyon.net/item/super-store-finder/3630922", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://packetstormsecurity.com/files/173302/Super-Store-Finder-PHP-Script-3.6-SQL-Injection.html", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "VDB Entry"]}]}