{"cve_id": "CVE-2023-0813", "published_date": "2023-09-15T21:15:08.953", "last_modified_date": "2024-11-21T07:37:53.203", "descriptions": [{"lang": "en", "value": "A flaw was found in the Network Observability plugin for OpenShift console. Unless the Loki authToken configuration is set to FORWARD mode, authentication is no longer enforced, allowing any user who can connect to the OpenShift Console in an OpenShift cluster to retrieve flows without authentication."}, {"lang": "es", "value": "Se encontró una falla en el complemento Network Observability para la consola OpenShift. A menos que la configuración de Loki authToken esté configurada en modo FORWARD, la autenticación ya no se aplica, lo que permite que cualquier usuario que pueda conectarse a la consola OpenShift en un clúster de OpenShift recupere flujos sin autenticación."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2023:0786", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://access.redhat.com/security/cve/CVE-2023-0813", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2169468", "source": "<EMAIL>", "tags": ["Issue Tracking", "Vendor Advisory"]}]}