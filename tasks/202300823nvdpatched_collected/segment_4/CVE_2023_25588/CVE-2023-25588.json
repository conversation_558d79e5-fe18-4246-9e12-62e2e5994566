{"cve_id": "CVE-2023-25588", "published_date": "2023-09-14T21:15:10.320", "last_modified_date": "2024-11-21T07:49:46.910", "descriptions": [{"lang": "en", "value": "A flaw was found in Binutils. The field `the_bfd` of `asymbol`struct is uninitialized in the `bfd_mach_o_get_synthetic_symtab` function, which may lead to an application crash and local denial of service."}, {"lang": "es", "value": "Se encontró una falla en Binutils. El campo `the_bfd` de `asymbol`struct no está inicializado en la función `bfd_mach_o_get_synthetic_symtab`, lo que puede provocar un bloqueo de la aplicación y una denegación de servicio local."}], "references": [{"url": "https://access.redhat.com/security/cve/CVE-2023-25588", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2167505", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Patch"]}, {"url": "https://security.netapp.com/advisory/ntap-20231103-0003/", "source": "<EMAIL>", "tags": []}, {"url": "https://sourceware.org/bugzilla/show_bug.cgi?id=29677", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Patch"]}, {"url": "https://sourceware.org/git/gitweb.cgi?p=binutils-gdb.git;h=d12f8998d2d086f0a6606589e5aedb7147e6f2f1", "source": "<EMAIL>", "tags": ["Mailing List", "Patch"]}]}