{"cve_id": "CVE-2023-4039", "published_date": "2023-09-13T09:15:15.690", "last_modified_date": "2025-02-13T17:17:14.717", "descriptions": [{"lang": "en", "value": "**DISPUTED**A failure in the -fstack-protector feature in GCC-based toolchains \nthat target AArch64 allows an attacker to exploit an existing buffer \noverflow in dynamically-sized local variables in your application \nwithout this being detected. This stack-protector failure only applies \nto C99-style dynamically-sized local variables or those created using \nalloca(). The stack-protector operates as intended for statically-sized \nlocal variables.\n\nThe default behavior when the stack-protector \ndetects an overflow is to terminate your application, resulting in \ncontrolled loss of availability. An attacker who can exploit a buffer \noverflow without triggering the stack-protector might be able to change \nprogram flow control to cause an uncontrolled loss of availability or to\n go further and affect confidentiality or integrity. NOTE: The GCC project argues that this is a missed hardening bug and not a vulnerability by itself."}, {"lang": "es", "value": "Una falla en la función -fstack-protector en cadenas de herramientas basadas en GCC que apuntan a AArch64 permite a un atacante explotar un Desbordamiento de Búfer existente en variables locales de tamaño dinámico en su aplicación sin que esto sea detectado. Esta falla del protector de pila solo se aplica a variables locales de tamaño dinámico estilo C99 o aquellas creadas usando alloca(). El protector de pila funciona según lo previsto para variables locales de tamaño estático. El comportamiento predeterminado cuando el protector de pila detecta un desbordamiento es finalizar su aplicación, lo que resulta en una pérdida controlada de disponibilidad. Un atacante que pueda aprovechar un Desbordamiento del Búfer sin activar el protector de pila podría cambiar el control de flujo del programa para provocar una pérdida incontrolada de disponibilidad o ir más allá y afectar la confidencialidad o la integridad."}], "references": [{"url": "https://developer.arm.com/Arm%20Security%20Center/GCC%20Stack%20Protector%20Vulnerability%20AArch64", "source": "<EMAIL>", "tags": ["Exploit", "Patch", "Third Party Advisory"]}, {"url": "https://github.com/metaredteam/external-disclosures/security/advisories/GHSA-x7ch-h5rf-w2mf", "source": "<EMAIL>", "tags": ["Exploit", "Patch", "Third Party Advisory"]}]}