{"cve_id": "CVE-2022-47631", "published_date": "2023-09-14T22:15:07.733", "last_modified_date": "2024-11-21T07:32:17.147", "descriptions": [{"lang": "en", "value": "Razer Synapse through 3.7.1209.121307 allows privilege escalation due to an unsafe installation path and improper privilege management. Attackers can place DLLs into %PROGRAMDATA%\\Razer\\Synapse3\\Service\\bin if they do so before the service is installed and if they deny write access for the SYSTEM user. Although the service will not start if it detects malicious DLLs in this directory, attackers can exploit a race condition and replace a valid DLL (i.e., a copy of a legitimate Razer DLL) with a malicious DLL after the service has already checked the file. As a result, local Windows users can abuse the Razer driver installer to obtain administrative privileges on Windows."}, {"lang": "es", "value": "Razer Synapse hasta 3.7.1209.121307 permite la escalada de privilegios debido a una ruta de instalación insegura y una gestión de privilegios inadecuada. Los atacantes pueden colocar archivos DLL en %PROGRAMDATA%\\Razer\\Synapse3\\Service\\bin si lo hacen antes de que se instale el servicio y si niegan el acceso de escritura al usuario SISTEMA. Aunque el servicio no se iniciará si detecta archivos DLL maliciosos en este directorio, los atacantes pueden aprovechar una condición de ejecución y reemplazar un DLL válido (es decir, una copia de una DLL legítimo de Razer) con una DLL malicioso después de que el servicio ya haya verificado el archivo. Como resultado, los usuarios locales de Windows pueden abusar del instalador del controlador Razer para obtener privilegios administrativos en Windows."}], "references": [{"url": "http://packetstormsecurity.com/files/174696/Razer-Synapse-Race-Condition-DLL-Hijacking.html", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "VDB Entry"]}, {"url": "http://seclists.org/fulldisclosure/2023/Sep/6", "source": "<EMAIL>", "tags": ["Exploit", "Mailing List", "Third Party Advisory"]}, {"url": "https://www.syss.de/fileadmin/dokumente/Publikationen/Advisories/SYSS-2023-002.txt", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}