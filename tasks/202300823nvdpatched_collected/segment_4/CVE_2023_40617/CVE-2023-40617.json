{"cve_id": "CVE-2023-40617", "published_date": "2023-09-13T22:15:07.733", "last_modified_date": "2024-11-21T08:19:49.850", "descriptions": [{"lang": "en", "value": "A reflected cross-site scripting (XSS) vulnerability in OpenKnowledgeMaps Head Start 7 allows remote attackers to execute arbitrary JavaScript in the web browser of a user, by including a malicious payload into the 'file' parameter in 'displayPDF.php'."}, {"lang": "es", "value": "Una vulnerabilidad Cross-Site Scripting (XSS) Reflejada en OpenKnowledgeMaps Head Start 7 permite a atacantes remotos ejecutar JavaScript arbitrario en el navegador web de un usuario, al incluir un payload malicioso en el parámetro 'file' en 'displayPDF.php'."}], "references": [{"url": "https://github.com/dub-flow/vulnerability-research/tree/main/CVE-2023-40617", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}