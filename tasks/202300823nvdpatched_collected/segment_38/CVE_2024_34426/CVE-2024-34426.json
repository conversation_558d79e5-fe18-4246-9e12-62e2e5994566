{"cve_id": "CVE-2024-34426", "published_date": "2024-05-14T15:38:59.963", "last_modified_date": "2024-11-21T09:18:38.983", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in Benoti Brozzme Scroll Top allows Stored XSS.This issue affects Brozzme Scroll Top: from n/a through 1.8.5.\n\n"}, {"lang": "es", "value": "La vulnerabilidad de neutralización inadecuada de la entrada durante la generación de páginas web ('cross-site Scripting') en Benoti Brozzme Scroll Top permite almacenar XSS. Este problema afecta a Brozzme Scroll Top: desde n/a hasta 1.8.5."}], "references": [{"url": "https://patchstack.com/database/vulnerability/brozzme-scroll-top/wordpress-brozzme-scroll-top-plugin-1-8-5-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}