{"cve_id": "CVE-2024-34437", "published_date": "2024-05-14T15:39:05.053", "last_modified_date": "2025-03-06T16:24:16.733", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in 10Web Form Builder Team Form Maker by 10Web allows Stored XSS.This issue affects Form Maker by 10Web: from n/a through 1.15.24.\n\n"}, {"lang": "es", "value": "La vulnerabilidad de neutralización inadecuada de la entrada durante la generación de páginas web ('cross-site Scripting') en 10Web Form Builder Team Form Maker de 10Web permite almacenar XSS. Este problema afecta a Form Maker de 10Web: desde n/a hasta 1.15.24."}], "references": [{"url": "https://patchstack.com/database/vulnerability/form-maker/wordpress-form-maker-by-10web-plugin-1-15-24-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}