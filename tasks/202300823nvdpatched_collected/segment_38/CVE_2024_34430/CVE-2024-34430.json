{"cve_id": "CVE-2024-34430", "published_date": "2024-05-14T15:39:02.173", "last_modified_date": "2024-11-21T09:18:39.460", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in Rashed Latif TT Custom Post Type Creator allows Stored XSS.This issue affects TT Custom Post Type Creator: from n/a through 1.0.\n\n"}, {"lang": "es", "value": "La vulnerabilidad de neutralización inadecuada de la entrada durante la generación de páginas web ('Cross-site Scripting') en Rashed Latif TT Custom Post Type Creator permite almacenar XSS. Este problema afecta a TT Custom Post Type Creator: desde n/a hasta 1.0."}], "references": [{"url": "https://patchstack.com/database/vulnerability/tt-custom-post-type-creator/wordpress-tt-custom-post-type-creator-plugin-1-0-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}