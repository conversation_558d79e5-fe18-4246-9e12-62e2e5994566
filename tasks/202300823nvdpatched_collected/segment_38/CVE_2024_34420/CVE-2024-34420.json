{"cve_id": "CVE-2024-34420", "published_date": "2024-05-14T15:38:56.980", "last_modified_date": "2024-11-21T09:18:38.243", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in talspotim Comments Evolved for WordPress allows Stored XSS.This issue affects Comments Evolved for WordPress: from n/a through 1.6.3.\n\n"}, {"lang": "es", "value": "La vulnerabilidad de neutralización inadecuada de la entrada durante la generación de páginas web ('cross-site Scripting') en talspotim Comments Evolved for WordPress permite almacenar XSS. Este problema afecta a Comments Evolved for WordPress: desde n/a hasta 1.6.3."}], "references": [{"url": "https://patchstack.com/database/vulnerability/gplus-comments/wordpress-comments-evolved-for-wordpress-plugin-1-6-3-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}