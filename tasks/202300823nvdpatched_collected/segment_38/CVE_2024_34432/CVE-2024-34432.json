{"cve_id": "CVE-2024-34432", "published_date": "2024-05-14T15:39:03.053", "last_modified_date": "2025-03-06T16:47:25.203", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in BetterAddons Better Elementor Addons better-elementor-addons allows Stored XSS.This issue affects Better Elementor Addons: from n/a through 1.4.4.\n\n"}, {"lang": "es", "value": "Vulnerabilidad de neutralización inadecuada de la entrada durante la generación de páginas web ('Cross-site Scripting') en BetterAddons Better Elementor Addons best-elementor-addons permite almacenar XSS. Este problema afecta a Better Elementor Addons: desde n/a hasta 1.4.4."}], "references": [{"url": "https://patchstack.com/database/vulnerability/better-elementor-addons/wordpress-better-elementor-addons-plugin-1-4-4-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}