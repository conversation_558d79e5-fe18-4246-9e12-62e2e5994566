{"cve_id": "CVE-2024-34365", "published_date": "2024-05-14T15:38:46.400", "last_modified_date": "2025-07-10T16:28:27.763", "descriptions": [{"lang": "en", "value": "** UNSUPPORTED WHEN ASSIGNED ** Improper Input Validation vulnerability in Apache Karaf Cave.This issue affects all versions of Apache Karaf Cave.\n\nAs this project is retired, we do not plan to release a version that fixes this issue. Users are recommended to find an alternative or restrict access to the instance to trusted users.NOTE: This vulnerability only affects products that are no longer supported by the maintainer."}, {"lang": "es", "value": "** NO SOPORTADO CUANDO ESTÁ ASIGNADO ** Vulnerabilidad de validación de entrada incorrecta en Apache Karaf Cave. Este problema afecta a todas las versiones de Apache Karaf Cave. Como este proyecto está retirado, no planeamos lanzar una versión que solucione este problema. Se recomienda a los usuarios buscar una alternativa o restringir el acceso a la instancia a usuarios confiables. NOTA: Esta vulnerabilidad solo afecta a los productos que ya no son compatibles con el fabricante."}], "references": [{"url": "http://www.openwall.com/lists/oss-security/2024/05/09/5", "source": "<EMAIL>", "tags": ["Mailing List", "Third Party Advisory"]}, {"url": "https://karaf.apache.org/security/cve-2024-34365.txt", "source": "<EMAIL>", "tags": ["Vendor Advisory", "Mailing List"]}]}