{"cve_id": "CVE-2024-34429", "published_date": "2024-05-14T15:39:01.703", "last_modified_date": "2024-11-21T09:18:39.350", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in Orchestrated Corona Virus (COVID-19) Banner & Live Data allows Stored XSS.This issue affects Corona Virus (COVID-19) Banner & Live Data: from n/a through *******.\n\n"}, {"lang": "es", "value": "La neutralización inadecuada de la entrada durante la vulnerabilidad de generación de páginas web ('Cross-site Scripting') en Orchestrated Corona Virus (COVID-19) Banner &amp; Live Data permite almacenar XSS. Este problema afecta los datos en vivo y los banners del virus Corona (COVID-19): de n/a hasta *******."}], "references": [{"url": "https://patchstack.com/database/vulnerability/corona-virus-covid-19-banner/wordpress-simple-website-banner-plugin-1-8-0-2-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}