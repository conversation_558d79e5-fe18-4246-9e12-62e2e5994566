{"cve_id": "CVE-2024-34419", "published_date": "2024-05-14T15:38:56.523", "last_modified_date": "2024-11-21T09:18:38.133", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in <PERSON> Configure Login Timeout allows Stored XSS.This issue affects Configure Login Timeout: from n/a through 1.0.\n\n"}, {"lang": "es", "value": "La vulnerabilidad de neutralización inadecuada de la entrada durante la generación de páginas web ('cross-site Scripting') en <PERSON> Configure Login Timeout permite almacenar XSS. Este problema afecta a Configure Login Timeout: desde n/a hasta 1.0."}], "references": [{"url": "https://patchstack.com/database/vulnerability/configure-login-timeout/wordpress-configure-login-timeout-plugin-1-0-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}