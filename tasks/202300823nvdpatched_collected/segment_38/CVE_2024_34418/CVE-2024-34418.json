{"cve_id": "CVE-2024-34418", "published_date": "2024-05-14T15:38:56.067", "last_modified_date": "2024-11-21T09:18:38.007", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in Tech9logy Creators WPCS ( WordPress Custom Search ) allows Stored XSS.This issue affects WPCS ( WordPress Custom Search ): from n/a through 1.1.\n\n"}, {"lang": "es", "value": "La vulnerabilidad de neutralización inadecuada de la entrada durante la generación de páginas web ('Cross-site Scripting') en Tech9logy Creators WPCS (WordPress Custom Search) permite almacenar XSS. Este problema afecta a WPCS (WordPress Custom Search): desde n/a hasta 1.1."}], "references": [{"url": "https://patchstack.com/database/vulnerability/wpcs-wp-custom-search/wordpress-wpcs-wordpress-custom-search-plugin-1-1-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}