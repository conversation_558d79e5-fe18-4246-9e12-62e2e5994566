{"cve_id": "CVE-2024-34431", "published_date": "2024-05-14T15:39:02.617", "last_modified_date": "2024-11-21T09:18:39.577", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in WP-etracker WP etracker allows Reflected XSS.This issue affects WP etracker: from n/a through 1.0.2.\n\n"}, {"lang": "es", "value": "Vulnerabilidad de neutralización inadecuada de la entrada durante la generación de páginas web ('Cross-site Scripting') en WP-etracker WP etracker permite Reflected XSS. Este problema afecta a WP etracker: desde n/a hasta 1.0.2."}], "references": [{"url": "https://patchstack.com/database/vulnerability/wp-etracker/wordpress-wp-etracker-plugin-1-0-2-reflected-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}