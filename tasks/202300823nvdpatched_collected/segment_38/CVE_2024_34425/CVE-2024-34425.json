{"cve_id": "CVE-2024-34425", "published_date": "2024-05-14T15:38:59.540", "last_modified_date": "2024-11-21T09:18:38.867", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in Phil Baylog QuickieBar allows Stored XSS.This issue affects QuickieBar: from n/a through 1.8.4.\n\n"}, {"lang": "es", "value": "La vulnerabilidad de neutralización inadecuada de la entrada durante la generación de páginas web ('cross-site Scripting') en Phil Baylog QuickieBar permite almacenar XSS. Este problema afecta a QuickieBar: desde n/a hasta 1.8.4."}], "references": [{"url": "https://patchstack.com/database/vulnerability/quickiebar/wordpress-quickiebar-plugin-1-8-4-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}