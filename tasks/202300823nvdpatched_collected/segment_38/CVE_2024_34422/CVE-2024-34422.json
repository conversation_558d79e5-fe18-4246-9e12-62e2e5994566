{"cve_id": "CVE-2024-34422", "published_date": "2024-05-14T15:38:57.980", "last_modified_date": "2024-11-21T09:18:38.500", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in trinhtuantai Viet Affiliate Link allows Stored XSS.This issue affects Viet Affiliate Link: from n/a through 1.2.\n\n"}, {"lang": "es", "value": "La vulnerabilidad de neutralización inadecuada de la entrada durante la generación de páginas web ('cross-site Scripting') en trinhtuantai Viet Affiliate Link permite almacenar XSS. Este problema afecta a Viet Affiliate Link: desde n/a hasta 1.2."}], "references": [{"url": "https://patchstack.com/database/vulnerability/viet-affiliate-link/wordpress-viet-affiliate-link-plugin-1-2-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}