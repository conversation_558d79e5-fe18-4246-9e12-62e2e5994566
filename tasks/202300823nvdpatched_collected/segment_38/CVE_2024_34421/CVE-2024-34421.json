{"cve_id": "CVE-2024-34421", "published_date": "2024-05-14T15:38:57.570", "last_modified_date": "2024-11-21T09:18:38.377", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting') vulnerability in wpsurface BlogLentor allows Stored XSS.This issue affects BlogLentor: from n/a through 1.0.8.\n\n"}, {"lang": "es", "value": "La vulnerabilidad de neutralización inadecuada de la entrada durante la generación de páginas web ('cross-site Scripting') en wpsurface BlogLentor permite almacenar XSS. Este problema afecta a BlogLentor: desde n/a hasta 1.0.8."}], "references": [{"url": "https://patchstack.com/database/vulnerability/bloglentor-for-elementor/wordpress-bloglentor-blog-designer-pack-for-elementor-plugin-1-0-8-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": []}]}