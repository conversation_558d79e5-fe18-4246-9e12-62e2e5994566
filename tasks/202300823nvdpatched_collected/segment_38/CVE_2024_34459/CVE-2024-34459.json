{"cve_id": "CVE-2024-34459", "published_date": "2024-05-14T15:39:11.917", "last_modified_date": "2024-11-21T09:18:43.590", "descriptions": [{"lang": "en", "value": "An issue was discovered in xmllint (from libxml2) before 2.11.8 and 2.12.x before 2.12.7. Formatting error messages with xmllint --htmlout can result in a buffer over-read in xmlHTMLPrintFileContext in xmllint.c."}, {"lang": "es", "value": "Se descubrió un problema en xmllint (de libxml2) anterior a 2.11.8 y 2.12.x anterior a 2.12.7. Formatear mensajes de error con xmllint --htmlout puede provocar una lectura excesiva del búfer en xmlHTMLPrintFileContext en xmllint.c."}], "references": [{"url": "https://gitlab.gnome.org/GNOME/libxml2/-/issues/720", "source": "<EMAIL>", "tags": []}, {"url": "https://gitlab.gnome.org/GNOME/libxml2/-/releases/v2.11.8", "source": "<EMAIL>", "tags": []}, {"url": "https://gitlab.gnome.org/GNOME/libxml2/-/releases/v2.12.7", "source": "<EMAIL>", "tags": []}, {"url": "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/5HVUXKYTBWT3G5DEEQX62STJQBY367NL/", "source": "<EMAIL>", "tags": []}, {"url": "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/INKSSLW5VMZIXHRPZBAW4TJUX5SQKARG/", "source": "<EMAIL>", "tags": []}, {"url": "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/VRDJCNQP32LV56KESUQ5SNZKAJWSZZRI/", "source": "<EMAIL>", "tags": []}]}