{"cve_id": "CVE-2024-5119", "published_date": "2024-05-20T06:15:09.013", "last_modified_date": "2025-02-10T14:34:22.703", "descriptions": [{"lang": "en", "value": "A vulnerability was found in SourceCodester Event Registration System 1.0 and classified as critical. This issue affects some unknown processing of the file /classes/Master.php?f=load_registration. The manipulation of the argument last_id/event_id leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used. The associated identifier of this vulnerability is VDB-265199."}, {"lang": "es", "value": "Una vulnerabilidad fue encontrada en SourceCodester Event Registration System 1.0 y clasificada como crítica. Este problema afecta algún procesamiento desconocido del archivo /classes/Master.php?f=load_registration. La manipulación del argumento last_id/event_id conduce a la inyección de SQL. El ataque puede iniciarse de forma remota. El exploit ha sido divulgado al público y puede utilizarse. El identificador asociado de esta vulnerabilidad es VDB-265199."}], "references": [{"url": "https://github.com/BurakSevben/CVEs/blob/main/Event%20Registration%20System/Event%20Registration%20System%20-%20SQL%20Injection%20-%202.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.265199", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.265199", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.338613", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}