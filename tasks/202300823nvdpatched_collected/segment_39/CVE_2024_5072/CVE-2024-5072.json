{"cve_id": "CVE-2024-5072", "published_date": "2024-05-17T16:15:08.300", "last_modified_date": "2025-03-28T16:22:30.187", "descriptions": [{"lang": "en", "value": "Improper input validation in PAM JIT elevation feature in Devolutions Server 2024.1.11.0 and earlier allows an authenticated user with access to the PAM JIT elevation feature to manipulate the LDAP filter query via a specially crafted request."}, {"lang": "es", "value": " La validación de entrada incorrecta en la función de elevación PAM JIT en Devolutions Server 2024.1.11.0 y versiones anteriores permite que un usuario autenticado con acceso a la función de elevación PAM JIT manipule la consulta del filtro LDAP a través de una solicitud especialmente manipulada."}], "references": [{"url": "https://devolutions.net/security/advisories/DEVO-2024-0007", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}