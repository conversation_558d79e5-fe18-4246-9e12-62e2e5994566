{"cve_id": "CVE-2024-5063", "published_date": "2024-05-17T19:15:07.683", "last_modified_date": "2025-03-03T16:05:23.833", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Online Course Registration System 3.1. It has been declared as critical. This vulnerability affects unknown code of the file /admin/index.php. The manipulation of the argument username/password leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used. VDB-264922 is the identifier assigned to this vulnerability."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Online Course Registration System 3.1. Ha sido declarada crítica. Esta vulnerabilidad afecta a un código desconocido del archivo /admin/index.php. La manipulación del argumento username/password conduce a la inyección de SQL. El ataque se puede iniciar de forma remota. El exploit ha sido divulgado al público y puede utilizarse. VDB-264922 es el identificador asignado a esta vulnerabilidad."}], "references": [{"url": "https://github.com/BurakSevben/CVEs/blob/main/Online%20Course%20Registration%20System/Online%20Course%20Registration%20System%20-%20Authentication%20Bypass.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.264922", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.264922", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.336236", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}