{"cve_id": "CVE-2024-4656", "published_date": "2024-05-15T02:15:10.653", "last_modified_date": "2024-11-21T09:43:19.090", "descriptions": [{"lang": "en", "value": "The Import and export users and customers plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the user agent header in all versions up to, and including, ******** due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with administrator access and higher, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento The Import and export users and customers para WordPress es vulnerable a Cross-Site Scripting Almacenado a través del encabezado del agente de usuario en todas las versiones hasta la ******** incluida debido a una sanitización de entrada y un escape de salida insuficientes. Esto hace posible que atacantes autenticados, con acceso de administrador y superior, inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&new=3085346%40import-users-from-csv-with-meta%2Ftrunk&old=3078277%40import-users-from-csv-with-meta%2Ftrunk&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/af742451-b2d6-445a-9a10-e950490f6c7c?source=cve", "source": "<EMAIL>", "tags": []}]}