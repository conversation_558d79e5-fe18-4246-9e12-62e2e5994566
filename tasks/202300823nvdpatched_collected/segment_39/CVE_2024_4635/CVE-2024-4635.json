{"cve_id": "CVE-2024-4635", "published_date": "2024-05-16T06:15:12.090", "last_modified_date": "2024-11-21T09:43:15.457", "descriptions": [{"lang": "en", "value": "The Menu Icons by ThemeIsle plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘add_mime_type’ function in versions up to, and including, 0.13.13 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with author-level permissions and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Menu Icons de ThemeIsle para WordPress es vulnerable a Cross-Site Scripting Almacenado a través de la función 'add_mime_type' en versiones hasta la 0.13.13 incluida, debido a una desinfección de entrada y un escape de salida insuficientes. Esto permite que atacantes autenticados, con permisos de nivel de autor y superiores, inyecten secuencias de comandos web arbitrarias en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/menu-icons/tags/0.13.13/vendor/codeinwp/icon-picker/includes/types/svg.php#L69", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3086753/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/*************-4e4c-8eb3-743bc402ea1b?source=cve", "source": "<EMAIL>", "tags": []}]}