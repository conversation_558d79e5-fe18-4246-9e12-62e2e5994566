{"cve_id": "CVE-2024-5098", "published_date": "2024-05-19T06:15:06.320", "last_modified_date": "2025-02-10T14:28:19.707", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in SourceCodester Simple Inventory System 1.0 and classified as critical. Affected by this vulnerability is an unknown functionality of the file login.php. The manipulation of the argument username leads to sql injection. The exploit has been disclosed to the public and may be used. The identifier VDB-265081 was assigned to this vulnerability."}, {"lang": "es", "value": "Una vulnerabilidad ha sido encontrada en SourceCodester Simple Inventory System 1.0 y clasificada como crítica. Una función desconocida del archivo login.php es afectada por esta vulnerabilidad. La manipulación del argumento username conduce a la inyección de SQL. El exploit ha sido divulgado al público y puede utilizarse. A esta vulnerabilidad se le asignó el identificador VDB-265081."}], "references": [{"url": "https://github.com/rockersiyuan/CVE/blob/main/SourceCodester%20Simple%20Inventory%20System%20Sql%20Inject-1.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.265081", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.265081", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.337056", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}