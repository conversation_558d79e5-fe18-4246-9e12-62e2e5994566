{"cve_id": "CVE-2024-5064", "published_date": "2024-05-17T19:15:08.337", "last_modified_date": "2025-03-03T16:12:05.970", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Online Course Registration System 3.1. It has been rated as critical. This issue affects some unknown processing of the file news-details.php. The manipulation of the argument nid leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used. The associated identifier of this vulnerability is VDB-264923."}, {"lang": "es", "value": " Se encontró una vulnerabilidad en PHPGurukul Online Course Registration System 3.1. Ha sido calificada como crítica. Este problema afecta un procesamiento desconocido del archivo news-details.php. La manipulación del argumento nid conduce a la inyección de SQL. El ataque puede iniciarse de forma remota. El exploit ha sido divulgado al público y puede utilizarse. El identificador asociado de esta vulnerabilidad es VDB-264923."}], "references": [{"url": "https://github.com/BurakSevben/CVEs/blob/main/Online%20Course%20Registration%20System/Online%20Course%20Registration%20System%20-%20SQL%20Injection%20-%202%20(Unauthenticated).md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.264923", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.264923", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.336238", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}