{"cve_id": "CVE-2024-4893", "published_date": "2024-05-15T03:15:14.493", "last_modified_date": "2024-11-21T09:43:48.507", "descriptions": [{"lang": "en", "value": "DigiWin EasyFlow .NET lacks validation for certain input parameters, allowing remote attackers to inject arbitrary SQL commands. This vulnerability enables unauthorized access to read, modify, and delete database records, as well as execute system commands."}, {"lang": "es", "value": "DigiWin EasyFlow .NET carece de validación para ciertos parámetros de entrada, lo que permite a atacantes remotos inyectar comandos SQL arbitrarios. Esta vulnerabilidad permite el acceso no autorizado para leer, modificar y eliminar registros de bases de datos, así como ejecutar comandos del sistema."}], "references": [{"url": "https://www.twcert.org.tw/en/cp-139-7801-67d07-2.html", "source": "<EMAIL>", "tags": []}, {"url": "https://www.twcert.org.tw/tw/cp-132-7800-843f1-1.html", "source": "<EMAIL>", "tags": []}]}