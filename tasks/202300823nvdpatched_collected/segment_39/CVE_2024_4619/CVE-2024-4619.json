{"cve_id": "CVE-2024-4619", "published_date": "2024-05-21T11:15:09.397", "last_modified_date": "2025-01-24T14:20:44.467", "descriptions": [{"lang": "en", "value": "The Elementor Website Builder – More than Just a Page Builder plugin for WordPress is vulnerable to DOM-Based Stored Cross-Site Scripting via the ‘hover_animation’ parameter in versions up to, and including, 3.21.4 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with contributor-level permissions and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Elementor Website Builder – More than Just a Page Builder para WordPress es vulnerable a Cross Site Scripting Almacenado basado en DOM a través del parámetro 'hover_animation' en versiones hasta la 3.21.4 incluida debido a una limpieza de entrada insuficiente y un escape de salida . Esto hace posible que atacantes autenticados, con permisos de nivel de colaborador y superiores, inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/elementor/trunk/includes/widgets/image-box.php#L696", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3089420", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/c7e1028e-e04b-46c4-b574-889d9fc1069d?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}