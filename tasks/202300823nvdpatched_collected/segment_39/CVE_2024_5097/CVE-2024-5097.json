{"cve_id": "CVE-2024-5097", "published_date": "2024-05-19T03:15:06.433", "last_modified_date": "2025-02-10T14:29:03.777", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as problematic, was found in SourceCodester Simple Inventory System 1.0. Affected is an unknown function of the file /tableedit.php#page=editprice. The manipulation of the argument itemnumber leads to cross-site request forgery. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used. The identifier of this vulnerability is VDB-265080."}, {"lang": "es", "value": " Una vulnerabilidad fue encontrada en SourceCodester Simple Inventory System 1.0 y clasificada como problemática. Una función desconocida del archivo /tableedit.php#page=editprice es afectada por esta vulnerabilidad. La manipulación del argumento itemnumber conduce a cross-site request forgery. Es posible lanzar el ataque de forma remota. El exploit ha sido divulgado al público y puede utilizarse. El identificador de esta vulnerabilidad es VDB-265080."}], "references": [{"url": "https://github.com/rockersiyuan/CVE/blob/main/SourceCodester%20Simple%20Inventory%20System%20CSRF.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.265080", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.265080", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.337055", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}