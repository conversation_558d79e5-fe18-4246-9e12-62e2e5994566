{"cve_id": "CVE-2024-4546", "published_date": "2024-05-16T08:15:38.487", "last_modified_date": "2024-11-21T09:43:04.703", "descriptions": [{"lang": "en", "value": "The Custom Post Type Attachment plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'pdf_attachment' shortcode in all versions up to, and including, 3.4.5 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Custom Post Type Attachment para WordPress es vulnerable a la ejecución de Cross-Site Scripting Almacenado a través del código abreviado 'pdf_attachment' del complemento en todas las versiones hasta la 3.4.5 incluida, debido a una desinfección de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite que atacantes autenticados, con acceso de nivel de colaborador y superior, inyecten secuencias de comandos web arbitrarias en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset/3087121/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/f6ba2907-36f4-4c4d-9e25-d13d32e28690?source=cve", "source": "<EMAIL>", "tags": []}]}