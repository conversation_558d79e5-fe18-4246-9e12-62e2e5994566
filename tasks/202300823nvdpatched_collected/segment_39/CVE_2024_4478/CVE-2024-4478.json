{"cve_id": "CVE-2024-4478", "published_date": "2024-05-16T08:15:38.200", "last_modified_date": "2025-01-07T18:10:23.620", "descriptions": [{"lang": "en", "value": "The Happy Addons for Elementor plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the Image Stack Group widget in all versions up to, and including, 3.10.7 due to insufficient input sanitization and output escaping on user supplied 'tooltip_position' attribute. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Happy Addons para Elementor para WordPress es vulnerable a Cross-Site Scripting Almacenado a través del widget Image Stack Group en todas las versiones hasta la 3.10.7 incluida, debido a una desinfección de entrada insuficiente y al escape de salida en el atributo 'tooltip_position' proporcionado por el usuario. Esto hace posible que atacantes autenticados, con acceso de nivel de colaborador y superior, inyecten secuencias de comandos web arbitrarias en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/happy-elementor-addons/tags/3.10.7/widgets/image-stack-group/widget.php#L611", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3083138/#file584", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://wordpress.org/plugins/happy-elementor-addons/#developers", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/c7243f40-5cca-475a-bb27-44fab965bb0e?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}