{"cve_id": "CVE-2024-5116", "published_date": "2024-05-20T04:15:09.070", "last_modified_date": "2025-02-10T13:54:20.543", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in SourceCodester Online Examination System 1.0. Affected by this issue is some unknown functionality of the file save.php. The manipulation of the argument vote leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used. The identifier of this vulnerability is VDB-265196."}, {"lang": "es", "value": "Una vulnerabilidad fue encontrada en SourceCodester Online Examination System 1.0 y clasificada como crítica. Una función desconocida del archivo save.php es afectada por esta vulnerabilidad. La manipulación del argumento vote conduce a la inyección de SQL. El ataque puede lanzarse de forma remota. El exploit ha sido divulgado al público y puede utilizarse. El identificador de esta vulnerabilidad es VDB-265196."}], "references": [{"url": "https://github.com/polaris0x1/CVE/issues/3", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.265196", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.265196", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.338578", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}