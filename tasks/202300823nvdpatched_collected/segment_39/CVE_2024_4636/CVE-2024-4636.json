{"cve_id": "CVE-2024-4636", "published_date": "2024-05-15T07:15:48.207", "last_modified_date": "2024-11-21T09:43:15.583", "descriptions": [{"lang": "en", "value": "The Image Optimization by Optimole – <PERSON>zy Load, CDN, Convert WebP & AVIF plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘allow_meme_types’ function in versions up to, and including, 3.12.10 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with contributor-level permissions and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Image Optimization de Optimole – <PERSON>zy Load, CDN, Convert WebP y AVIF para WordPress es vulnerable a Cross-Site Scripting Almacenado a través de la función 'allow_meme_types' en versiones hasta la 3.12.10 incluida debido a una sanitización de entrada y salida insuficiente escapando. Esto hace posible que atacantes autenticados, con permisos de nivel de colaborador y superiores, inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/optimole-wp/tags/3.12.10/inc/admin.php#L1828", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3086306/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/be88566d-fc84-442d-bb34-834ad9f4465b?source=cve", "source": "<EMAIL>", "tags": []}]}