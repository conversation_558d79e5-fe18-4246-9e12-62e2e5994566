{"cve_id": "CVE-2024-5145", "published_date": "2024-05-20T23:15:08.533", "last_modified_date": "2025-02-10T13:57:18.723", "descriptions": [{"lang": "en", "value": "A vulnerability was found in SourceCodester Vehicle Management System up to 1.0 and classified as critical. This issue affects some unknown processing of the file /newdriver.php of the component HTTP POST Request Handler. The manipulation of the argument file leads to unrestricted upload. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used. The identifier VDB-265289 was assigned to this vulnerability."}, {"lang": "es", "value": "Una vulnerabilidad fue encontrada en SourceCodester Vehicle Management System hasta 1.0 y clasificada como crítica. Este problema afecta un procesamiento desconocido del archivo /newdriver.php del componente HTTP POST Request Handler. La manipulación del argumento file conduce a una carga sin restricciones. El ataque puede iniciarse de forma remota. El exploit ha sido divulgado al público y puede utilizarse. A esta vulnerabilidad se le asignó el identificador VDB-265289."}], "references": [{"url": "https://github.com/CveSecLook/cve/issues/38", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.265289", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.265289", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.339721", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}