{"cve_id": "CVE-2024-5117", "published_date": "2024-05-20T05:15:10.110", "last_modified_date": "2025-02-10T14:35:26.263", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in SourceCodester Event Registration System 1.0. This affects an unknown part of the file portal.php. The manipulation of the argument username/password leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used. The identifier VDB-265197 was assigned to this vulnerability."}, {"lang": "es", "value": "Una vulnerabilidad fue encontrada en SourceCodester Event Registration System 1.0 y clasificada como crítica. Una parte desconocida del archivo portal.php afecta a esta vulnerabilidad. La manipulación del argumento username/password conduce a la inyección de SQL. Es posible iniciar el ataque de forma remota. El exploit ha sido divulgado al público y puede utilizarse. A esta vulnerabilidad se le asignó el identificador VDB-265197."}], "references": [{"url": "https://github.com/BurakSevben/CVEs/blob/main/Event%20Registration%20System/Event%20Registration%20System%20-%20SQL%20Injection%20-%201.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.265197", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.265197", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}