{"cve_id": "CVE-2024-5136", "published_date": "2024-05-20T09:15:10.007", "last_modified_date": "2025-02-21T20:32:11.340", "descriptions": [{"lang": "en", "value": "A vulnerability classified as problematic has been found in PHPGurukul Directory Management System 1.0. Affected is an unknown function of the file /admin/search-directory.php.. The manipulation leads to cross site scripting. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used. The identifier of this vulnerability is VDB-265212."}, {"lang": "es", "value": " Una vulnerabilidad ha sido encontrada en PHPGurukul Directory Management System 1.0 y clasificada como problemática. Una función desconocida del archivo /admin/search-directory.php es afectada por esta operación. Es posible lanzar el ataque de forma remota. El exploit ha sido divulgado al público y puede utilizarse. El identificador de esta vulnerabilidad es VDB-265212."}], "references": [{"url": "https://github.com/BurakSevben/CVEs/blob/main/Directory%20Management%20System/Directory%20Management%20System%20-%20Cross-Site-Scripting%20-%201.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.265212", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.265212", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.339122", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}