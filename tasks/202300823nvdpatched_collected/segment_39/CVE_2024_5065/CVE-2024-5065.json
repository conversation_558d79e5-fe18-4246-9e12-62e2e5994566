{"cve_id": "CVE-2024-5065", "published_date": "2024-05-17T20:15:07.333", "last_modified_date": "2025-03-03T16:12:28.940", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in PHPGurukul Online Course Registration System 3.1. Affected is an unknown function of the file /onlinecourse/. The manipulation of the argument regno leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used. The identifier of this vulnerability is VDB-264924."}, {"lang": "es", "value": "Una vulnerabilidad ha sido encontrada en PHPGurukul Online Course Registration System 3.1 y clasificada como crítica. Una función desconocida del archivo /onlinecourse/ es afectada por esta vulnerabilidad. La manipulación del argumento regno conduce a la inyección de SQL. Es posible lanzar el ataque de forma remota. El exploit ha sido divulgado al público y puede utilizarse. El identificador de esta vulnerabilidad es VDB-264924."}], "references": [{"url": "https://github.com/BurakSevben/CVEs/blob/main/Online%20Course%20Registration%20System/Online%20Course%20Registration%20System%20-%20SQL%20Injection%20-%203%20(Unauthenticated).md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.264924", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.264924", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.336239", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}