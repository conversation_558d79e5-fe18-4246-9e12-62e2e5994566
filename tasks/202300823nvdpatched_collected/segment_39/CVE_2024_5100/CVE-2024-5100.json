{"cve_id": "CVE-2024-5100", "published_date": "2024-05-19T12:15:08.310", "last_modified_date": "2025-02-10T14:26:40.580", "descriptions": [{"lang": "en", "value": "A vulnerability was found in SourceCodester Simple Inventory System 1.0. It has been classified as critical. This affects an unknown part of the file tableedit.php. The manipulation of the argument from/to leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used. The associated identifier of this vulnerability is VDB-265083."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en SourceCodester Simple Inventory System 1.0. Ha sido clasificada como crítica. Una parte desconocida del archivo tableedit.php afecta a esta vulnerabilidad. La manipulación del argumento from/to conduce a la inyección SQL. Es posible iniciar el ataque de forma remota. El exploit ha sido divulgado al público y puede utilizarse. El identificador asociado de esta vulnerabilidad es VDB-265083."}], "references": [{"url": "https://github.com/rockersiyuan/CVE/blob/main/SourceCodester%20Simple%20Inventory%20System%20Sql%20Inject-3.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.265083", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.265083", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.337058", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}