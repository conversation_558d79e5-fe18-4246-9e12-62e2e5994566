{"cve_id": "CVE-2024-4875", "published_date": "2024-05-21T09:15:09.103", "last_modified_date": "2025-01-28T19:20:29.297", "descriptions": [{"lang": "en", "value": "The HT Mega – Absolute Addons For Elementor plugin for WordPress is vulnerable to unauthorized modification of data|loss of data due to a missing capability check on the 'ajax_dismiss' function in versions up to, and including, 2.5.2. This makes it possible for authenticated attackers, with subscriber-level permissions and above, to update options such as users_can_register, which can lead to unauthorized user registration."}, {"lang": "es", "value": " El complemento HT Mega – Absolute Addons For Elementor para WordPress es vulnerable a la modificación no autorizada de datos (pérdida de datos) debido a una falta de verificación de capacidad en la función 'ajax_dismiss' en versiones hasta la 2.5.2 incluida. Esto hace posible que atacantes autenticados, con permisos de nivel de suscriptor y superiores, actualicen opciones como users_can_register, lo que puede conducir al registro de usuarios no autorizados."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/ht-mega-for-elementor/trunk/admin/include/class.dynamic-notice.php#L52", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3088899/ht-mega-for-elementor/trunk/admin/include/class.dynamic-notice.php", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/bdd3868a-d741-42b4-bc7f-6fb5d33bb71b?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}