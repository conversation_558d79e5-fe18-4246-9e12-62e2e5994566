{"cve_id": "CVE-2024-5118", "published_date": "2024-05-20T05:15:10.387", "last_modified_date": "2025-02-10T14:34:49.027", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in SourceCodester Event Registration System 1.0 and classified as critical. This vulnerability affects unknown code of the file /admin/login.php. The manipulation of the argument username/password leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used. VDB-265198 is the identifier assigned to this vulnerability."}, {"lang": "es", "value": "Una vulnerabilidad fue encontrada en SourceCodester Event Registration System 1.0 y clasificada como crítica. Esta vulnerabilidad afecta a un código desconocido del archivo /admin/login.php. La manipulación del argumento username/password conduce a la inyección de SQL. El ataque se puede iniciar de forma remota. El exploit ha sido divulgado al público y puede utilizarse. VDB-265198 es el identificador asignado a esta vulnerabilidad."}], "references": [{"url": "https://github.com/BurakSevben/CVEs/blob/main/Event%20Registration%20System/Event%20Registration%20System%20-%20SQL%20Injection%20-%201.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.265198", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.265198", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.338612", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}