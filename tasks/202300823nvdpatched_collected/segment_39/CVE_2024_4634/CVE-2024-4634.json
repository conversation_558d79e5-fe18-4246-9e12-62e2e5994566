{"cve_id": "CVE-2024-4634", "published_date": "2024-05-16T11:15:49.153", "last_modified_date": "2025-01-30T16:01:17.150", "descriptions": [{"lang": "en", "value": "The Elementor Header & Footer Builder plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘hfe_svg_mime_types’ function in versions up to, and including, 1.6.28 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with contributor-level permissions and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Elementor Header &amp; Footer Builder para WordPress es vulnerable a Cross-Site Scripting Almacenado a través de la función 'hfe_svg_mime_types' en versiones hasta la 1.6.28 incluida, debido a una desinfección de entrada y un escape de salida insuficientes. Esto permite que atacantes autenticados, con permisos de nivel de colaborador y superiores, inyecten secuencias de comandos web arbitrarias en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/header-footer-elementor/tags/1.6.28/inc/widgets-manager/class-widgets-loader.php#L156", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3086402/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/f44bb823-bbf3-413b-82b5-a351609270bf?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}