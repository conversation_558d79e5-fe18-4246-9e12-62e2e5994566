{"cve_id": "CVE-2024-5099", "published_date": "2024-05-19T08:15:06.367", "last_modified_date": "2025-02-10T14:27:10.093", "descriptions": [{"lang": "en", "value": "A vulnerability was found in SourceCodester Simple Inventory System 1.0 and classified as critical. Affected by this issue is some unknown functionality of the file updateprice.php. The manipulation of the argument ITEM leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used. VDB-265082 is the identifier assigned to this vulnerability."}, {"lang": "es", "value": " Una vulnerabilidad fue encontrada en SourceCodester Simple Inventory System 1.0 y clasificada como crítica. Una función desconocida del archivo updateprice.php es afectada por esta vulnerabilidad. La manipulación del argumento ITEM conduce a la inyección SQL. El ataque puede lanzarse de forma remota. El exploit ha sido divulgado al público y puede utilizarse. VDB-265082 es el identificador asignado a esta vulnerabilidad."}], "references": [{"url": "https://github.com/rockersiyuan/CVE/blob/main/SourceCodester%20Simple%20Inventory%20System%20Sql%20Inject-2.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.265082", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.265082", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.337057", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}