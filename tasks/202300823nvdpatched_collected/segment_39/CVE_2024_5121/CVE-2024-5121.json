{"cve_id": "CVE-2024-5121", "published_date": "2024-05-20T07:15:09.167", "last_modified_date": "2025-02-10T14:33:01.420", "descriptions": [{"lang": "en", "value": "A vulnerability was found in SourceCodester Event Registration System 1.0. It has been declared as problematic. Affected by this vulnerability is an unknown functionality of the file /registrar/?page=registration. The manipulation of the argument e leads to cross site scripting. The attack can be launched remotely. The exploit has been disclosed to the public and may be used. The identifier VDB-265201 was assigned to this vulnerability."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en SourceCodester Event Registration System 1.0. Ha sido declarada problemática. Una funcionalidad desconocida del archivo /registrar/?page=registration es afectada por esta vulnerabilidad. La manipulación del argumento e conduce a cross site scripting. El ataque se puede lanzar de forma remota. El exploit ha sido divulgado al público y puede utilizarse. A esta vulnerabilidad se le asignó el identificador VDB-265201."}], "references": [{"url": "https://github.com/BurakSevben/CVEs/blob/main/Event%20Registration%20System/Event%20Registration%20System%20-%20Cross-Site-Scripting%20-%202.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.265201", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.265201", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.338617", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}