{"cve_id": "CVE-2024-4838", "published_date": "2024-05-16T11:15:49.343", "last_modified_date": "2024-11-21T09:43:42.687", "descriptions": [{"lang": "en", "value": "The ConvertPlus plugin for WordPress is vulnerable to PHP Object Injection in all versions up to, and including, 3.5.26 via deserialization of untrusted input from the 'settings_encoded' attribute of the 'smile_modal' shortcode. This makes it possible for authenticated attackers, with contributor-level access and above, to inject a PHP Object. No POP chain is present in the vulnerable plugin. If a POP chain is present via an additional plugin or theme installed on the target system, it could allow the attacker to delete arbitrary files, retrieve sensitive data, or execute code."}, {"lang": "es", "value": "El complemento ConvertPlus para WordPress es vulnerable a la inyección de objetos PHP en todas las versiones hasta la 3.5.26 incluida, a través de la deserialización de la entrada no confiable del atributo 'settings_encoded' del shortcode 'smile_modal'. Esto hace posible que atacantes autenticados, con acceso de nivel de colaborador y superior, inyecten un objeto PHP. No hay ninguna cadena POP presente en el complemento vulnerable. Si hay una cadena POP presente a través de un complemento o tema adicional instalado en el sistema de destino, podría permitir al atacante eliminar archivos arbitrarios, recuperar datos confidenciales o ejecutar código."}], "references": [{"url": "https://www.convertplug.com/plus/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/16f5a104-dce0-4249-91b9-67f99cce16d3?source=cve", "source": "<EMAIL>", "tags": []}]}