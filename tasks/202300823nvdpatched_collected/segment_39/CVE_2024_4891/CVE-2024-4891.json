{"cve_id": "CVE-2024-4891", "published_date": "2024-05-18T05:15:46.917", "last_modified_date": "2025-01-30T15:45:34.377", "descriptions": [{"lang": "en", "value": "The Essential Blocks – Page Builder Gutenberg Blocks, Patterns & Templates plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘tagName’ parameter in versions up to, and including, 4.5.12 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with contributor-level permissions and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": " El complemento Essential Blocks – Page Builder Gutenberg Blocks, Patterns &amp; Templates para WordPress es vulnerable a cross site scripting almacenado a través del parámetro 'tagName' en versiones hasta la 4.5.12 incluida debido a una sanitización de entrada y un escape de salida insuficientes. Esto hace posible que atacantes autenticados, con permisos de nivel de colaborador y superiores, inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/essential-blocks/trunk/blocks/AdvancedHeading.php#L115", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3087677/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/e1bcebb3-920b-40cc-aa5c-24a1f729b28d?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}