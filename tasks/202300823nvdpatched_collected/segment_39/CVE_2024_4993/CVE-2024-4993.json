{"cve_id": "CVE-2024-4993", "published_date": "2024-05-16T12:15:15.290", "last_modified_date": "2024-11-21T09:44:00.907", "descriptions": [{"lang": "en", "value": "Vulnerability in SiAdmin 1.1 that allows XSS via the /show.php query parameter. This vulnerability could allow a remote attacker to send a specially crafted URL to an authenticated user and thereby steal their cookie session credentials."}, {"lang": "es", "value": "Vulnerabilidad en SiAdmin 1.1 que permite XSS a través del parámetro de consulta /show.php. Esta vulnerabilidad podría permitir que un atacante remoto envíe una URL especialmente manipulada a un usuario autenticado y, de ese modo, robe sus credenciales de sesión de cookies."}], "references": [{"url": "https://www.incibe.es/en/incibe-cert/notices/aviso/multiple-vulnerabilities-siadmin", "source": "<EMAIL>", "tags": []}]}