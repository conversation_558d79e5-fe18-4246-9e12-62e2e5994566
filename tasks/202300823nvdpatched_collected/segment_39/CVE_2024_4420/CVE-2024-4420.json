{"cve_id": "CVE-2024-4420", "published_date": "2024-05-21T12:15:08.627", "last_modified_date": "2025-06-05T14:41:54.827", "descriptions": [{"lang": "en", "value": "There exists a Denial of service vulnerability in Tink-cc in versions prior to 2.1.3.   *  An adversary can crash binaries using the crypto::tink::<PERSON>sonKeysetReader in tink-cc by providing an input that is not an encoded JSON object, but still a valid encoded JSON element, for example a number or an array. This will crash as <PERSON><PERSON> just assumes any valid JSON input will contain an object.\n\n\n  *  An adversary can crash binaries using the crypto::tink::J<PERSON><PERSON>eysetReader in tink-cc by providing an input containing many nested JSON objects. This may result in a stack overflow.\n\n\nWe recommend upgrading to version 2.1.3 or above"}, {"lang": "es", "value": " Existe una vulnerabilidad de denegación de servicio en Tink-cc en versiones anteriores a la 2.1.3. * Un adversario puede bloquear archivos binarios usando crypto::tink::<PERSON><PERSON>KeysetReader en tink-cc al proporcionar una entrada que no es un objeto JSON codificado, pero que sigue siendo un elemento JSON codificado válido, por ejemplo, un número o una matriz. Esto fallará ya que Tink simplemente asume que cualquier entrada JSON válida contendrá un objeto. * Un adversario puede bloquear archivos binarios usando crypto::tink::<PERSON>sonKeysetReader en tink-cc al proporcionar una entrada que contiene muchos objetos JSON anidados. Esto puede provocar un desbordamiento de la pila. Recomendamos actualizar a la versión 2.1.3 o superior."}], "references": [{"url": "https://github.com/tink-crypto/tink-cc/issues/4", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch", "Third Party Advisory"]}]}