{"cve_id": "CVE-2024-4974", "published_date": "2024-05-16T10:15:11.973", "last_modified_date": "2025-02-18T18:41:36.180", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as problematic, was found in code-projects Simple Chat System 1.0. Affected is an unknown function of the file /register.php. The manipulation of the argument name leads to cross site scripting. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used. The identifier of this vulnerability is VDB-264540."}, {"lang": "es", "value": "Una vulnerabilidad clasificada como problemática fue encontrada en el proyecto de código Simple Chat System 1.0. Una función desconocida del archivo /register.php es afectada por esta vulnerabilidad. La manipulación del argumento name conduce a Cross Site Scripting. Es posible lanzar el ataque de forma remota. El exploit ha sido divulgado al público y puede utilizarse. El identificador de esta vulnerabilidad es VDB-264540."}], "references": [{"url": "https://github.com/BurakSevben/CVEs/blob/main/Simple%20Chat%20App/Simple%20Chat%20App%20-%20Cross-Site-Scripting-1.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.264540", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.264540", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.335205", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}