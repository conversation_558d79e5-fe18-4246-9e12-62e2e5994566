{"cve_id": "CVE-2024-4849", "published_date": "2024-05-18T06:15:08.310", "last_modified_date": "2024-11-21T09:43:43.877", "descriptions": [{"lang": "en", "value": "The WordPress Automatic Plugin plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘autoplay’ parameter in all versions up to, and including, 3.94.0 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": " El complemento WordPress Automatic Plugin para WordPress es vulnerable a cross site scripting almacenado a través del parámetro 'autoplay' en todas las versiones hasta la 3.94.0 incluida debido a una sanitización de entrada y un escape de salida insuficientes. Esto hace posible que atacantes autenticados, con acceso de nivel de Colaborador y superior, inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://codecanyon.net/item/wordpress-automatic-plugin/1904470", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/4be58bfa-d489-45f5-9169-db8bab718175?source=cve", "source": "<EMAIL>", "tags": []}]}