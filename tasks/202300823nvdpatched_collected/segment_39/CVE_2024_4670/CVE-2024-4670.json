{"cve_id": "CVE-2024-4670", "published_date": "2024-05-15T13:15:26.503", "last_modified_date": "2024-11-21T09:43:20.563", "descriptions": [{"lang": "en", "value": "The All-in-One Video Gallery plugin for WordPress is vulnerable to Local File Inclusion in all versions up to, and including, 3.6.5 via the aiovg_search_form shortcode. This makes it possible for authenticated attackers, with contributor-level access and above, to include and execute arbitrary files on the server, allowing the execution of any PHP code in those files. This can be used to bypass access controls, obtain sensitive data, or achieve code execution in cases where images and other “safe” file types can be uploaded and included."}, {"lang": "es", "value": "El complemento All-in-One Video Gallery para WordPress es vulnerable a la inclusión de archivos locales en todas las versiones hasta la 3.6.5 incluida, a través del código corto aiovg_search_form. Esto permite que atacantes autenticados, con acceso de nivel de colaborador y superior, incluyan y ejecuten archivos arbitrarios en el servidor, lo que permite la ejecución de cualquier código PHP en esos archivos. Esto se puede utilizar para eludir los controles de acceso, obtener datos confidenciales o lograr la ejecución de código en casos en los que se puedan cargar e incluir imágenes y otros tipos de archivos \"seguros\"."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset/3085217/all-in-one-video-gallery", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/e2793547-5edf-4d2a-bc3b-fcaeed62963d?source=cve", "source": "<EMAIL>", "tags": []}]}