{"cve_id": "CVE-2024-4972", "published_date": "2024-05-16T09:15:18.277", "last_modified_date": "2025-02-18T18:38:56.967", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in code-projects Simple Chat System 1.0. This affects an unknown part of the file /login.php. The manipulation of the argument email/password leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used. The identifier VDB-264537 was assigned to this vulnerability."}, {"lang": "es", "value": "Una vulnerabilidad ha sido encontrada en code-projects Simple Chat System 1.0 y clasificada como crítica. Esto afecta a una parte desconocida del archivo /login.php. La manipulación del argumento correo electrónico/contraseña conduce a la inyección de SQL. Es posible iniciar el ataque de forma remota. El exploit ha sido divulgado al público y puede utilizarse. A esta vulnerabilidad se le asignó el identificador VDB-264537."}], "references": [{"url": "https://github.com/BurakSevben/CVEs/blob/main/Simple%20Chat%20App/Simple%20Chat%20App%20-%20SQL%20Injection%20-%201.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.264537", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.264537", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.335199", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}