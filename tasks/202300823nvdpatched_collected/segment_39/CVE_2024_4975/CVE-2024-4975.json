{"cve_id": "CVE-2024-4975", "published_date": "2024-05-16T10:15:12.703", "last_modified_date": "2025-02-18T18:42:21.327", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as problematic, has been found in code-projects Simple Chat System 1.0. This issue affects some unknown processing of the component Message Handler. The manipulation leads to cross site scripting. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used. The associated identifier of this vulnerability is VDB-264539."}, {"lang": "es", "value": " Una vulnerabilidad clasificada como problemática fue encontrada en el proyecto de código Simple Chat System 1.0. Este problema afecta un procesamiento desconocido del componente Message Handler. La manipulación conduce a Cross Site Scripting. El ataque puede iniciarse de forma remota. El exploit ha sido divulgado al público y puede utilizarse. El identificador asociado de esta vulnerabilidad es VDB-264539."}], "references": [{"url": "https://github.com/BurakSevben/CVEs/blob/main/Simple%20Chat%20App/Simple%20Chat%20App%20-%20Cross-Site-Scripting-2.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.264539", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.264539", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.335206", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}