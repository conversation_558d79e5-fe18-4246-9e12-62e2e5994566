{"cve_id": "CVE-2024-5048", "published_date": "2024-05-17T14:15:21.970", "last_modified_date": "2025-03-03T16:28:15.043", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in code-projects Budget Management 1.0. Affected by this vulnerability is an unknown functionality of the file /index.php. The manipulation of the argument edit leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used. The identifier VDB-264745 was assigned to this vulnerability."}, {"lang": "es", "value": "Una vulnerabilidad fue encontrada en code-projects Budget Management 1.0 y clasificada como crítica. Una función desconocida del archivo /index.php es afectada por esta vulnerabilidad. La manipulación del argumento edit conduce a la inyección de SQL. El ataque se puede lanzar de forma remota. El exploit ha sido divulgado al público y puede utilizarse. A esta vulnerabilidad se le asignó el identificador VDB-264745."}], "references": [{"url": "https://github.com/BurakSevben/CVEs/blob/main/Budget%20Management%20App/Budget%20Management%20App%20-%20SQL%20Injection%20-%201.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.264745", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.264745", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.335666", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}