{"cve_id": "CVE-2024-4603", "published_date": "2024-05-16T16:15:10.643", "last_modified_date": "2024-11-21T09:43:11.753", "descriptions": [{"lang": "en", "value": "Issue summary: Checking excessively long DSA keys or parameters may be very\nslow.\n\nImpact summary: Applications that use the functions EVP_PKEY_param_check()\nor EVP_PKEY_public_check() to check a DSA public key or DSA parameters may\nexperience long delays. Where the key or parameters that are being checked\nhave been obtained from an untrusted source this may lead to a Denial of\nService.\n\nThe functions EVP_PKEY_param_check() or EVP_PKEY_public_check() perform\nvarious checks on DSA parameters. Some of those computations take a long time\nif the modulus (`p` parameter) is too large.\n\nTrying to use a very large modulus is slow and OpenSSL will not allow using\npublic keys with a modulus which is over 10,000 bits in length for signature\nverification. However the key and parameter check functions do not limit\nthe modulus size when performing the checks.\n\nAn application that calls EVP_PKEY_param_check() or EVP_PKEY_public_check()\nand supplies a key or parameters obtained from an untrusted source could be\nvulnerable to a Denial of Service attack.\n\nThese functions are not called by OpenSSL itself on untrusted DSA keys so\nonly applications that directly call these functions may be vulnerable.\n\nAlso vulnerable are the OpenSSL pkey and pkeyparam command line applications\nwhen using the `-check` option.\n\nThe OpenSSL SSL/TLS implementation is not affected by this issue.\n\nThe OpenSSL 3.0 and 3.1 FIPS providers are affected by this issue."}, {"lang": "es", "value": "Resumen del problema: la comprobación de claves o parámetros DSA excesivamente largos puede resultar muy lenta. Resumen de impacto: las aplicaciones que utilizan las funciones EVP_PKEY_param_check() o EVP_PKEY_public_check() para comprobar una clave pública de DSA o parámetros de DSA pueden experimentar grandes retrasos. Cuando la clave o los parámetros que se están verificando se obtuvieron de una fuente que no es confiable, esto puede dar lugar a una Denegación de Servicio. Las funciones EVP_PKEY_param_check() o EVP_PKEY_public_check() realizan varias comprobaciones de los parámetros DSA. Algunos de esos cálculos toman mucho tiempo si el módulo (parámetro `p`) es demasiado grande. Intentar utilizar un módulo muy grande es lento y OpenSSL no permitirá el uso de claves públicas con un módulo de más de 10.000 bits de longitud para la verificación de firmas. Sin embargo, las funciones de verificación de claves y parámetros no limitan el tamaño del módulo al realizar las verificaciones. Una aplicación que llama a EVP_PKEY_param_check() o EVP_PKEY_public_check() y proporciona una clave o parámetros obtenidos de una fuente que no es de confianza podría ser vulnerable a un ataque de denegación de servicio. OpenSSL no llama a estas funciones en claves DSA que no son de confianza, por lo que solo las aplicaciones que llaman directamente a estas funciones pueden ser vulnerables. También son vulnerables las aplicaciones de línea de comandos OpenSSL pkey y pkeyparam cuando se usa la opción `-check`. La implementación de OpenSSL SSL/TLS no se ve afectada por este problema. Los proveedores FIPS OpenSSL 3.0 y 3.1 se ven afectados por este problema."}], "references": [{"url": "https://github.com/openssl/openssl/commit/3559e868e58005d15c6013a0c1fd832e51c73397", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/openssl/openssl/commit/53ea06486d296b890d565fb971b2764fcd826e7e", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/openssl/openssl/commit/9c39b3858091c152f52513c066ff2c5a47969f0d", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/openssl/openssl/commit/da343d0605c826ef197aceedc67e8e04f065f740", "source": "<EMAIL>", "tags": []}, {"url": "https://www.openssl.org/news/secadv/20240516.txt", "source": "<EMAIL>", "tags": []}, {"url": "http://www.openwall.com/lists/oss-security/2024/05/16/2", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://security.netapp.com/advisory/ntap-20240621-0001/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}