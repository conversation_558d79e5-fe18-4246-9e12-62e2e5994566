{"cve_id": "CVE-2024-5120", "published_date": "2024-05-20T06:15:09.393", "last_modified_date": "2025-02-10T14:33:42.463", "descriptions": [{"lang": "en", "value": "A vulnerability was found in SourceCodester Event Registration System 1.0. It has been classified as critical. Affected is an unknown function of the file /registrar/?page=registration. The manipulation of the argument e leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used. The identifier of this vulnerability is VDB-265200."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en SourceCodester Event Registration System 1.0. Ha sido clasificada como crítica. Una función desconocida del archivo /registrar/?page=registration es afectada por esta vulnerabilidad. La manipulación del argumento e conduce a la inyección SQL. Es posible lanzar el ataque de forma remota. El exploit ha sido divulgado al público y puede utilizarse. El identificador de esta vulnerabilidad es VDB-265200."}], "references": [{"url": "https://github.com/BurakSevben/CVEs/blob/main/Event%20Registration%20System/Event%20Registration%20System%20-%20SQL%20Injection%20-%203.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.265200", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.265200", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.338614", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}