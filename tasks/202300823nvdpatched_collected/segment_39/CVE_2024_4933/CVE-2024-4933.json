{"cve_id": "CVE-2024-4933", "published_date": "2024-05-16T05:15:51.923", "last_modified_date": "2024-12-09T22:51:00.937", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in SourceCodester Simple Online Bidding System 1.0 and classified as critical. Affected by this vulnerability is an unknown functionality of the file /simple-online-bidding-system/admin/index.php?page=manage_product. The manipulation of the argument id leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used. The identifier VDB-264469 was assigned to this vulnerability."}, {"lang": "es", "value": "Una vulnerabilidad fue encontrada en SourceCodester Simple Online Bidding System 1.0 y clasificada como crítica. Una función desconocida del archivo /simple-online-bidding-system/admin/index.php?page=manage_product es afectada por esta vulnerabilidad. La manipulación del argumento id conduce a la inyección de SQL. El ataque se puede lanzar de forma remota. El exploit ha sido divulgado al público y puede utilizarse. A esta vulnerabilidad se le asignó el identificador VDB-264469."}], "references": [{"url": "https://github.com/rockersiyuan/CVE/blob/main/SourceCodester%20Simple%20Online%20Bidding%20System%20Sql%20Inject-4.md", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.264469", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.264469", "source": "<EMAIL>", "tags": ["VDB Entry"]}, {"url": "https://vuldb.com/?submit.335367", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}