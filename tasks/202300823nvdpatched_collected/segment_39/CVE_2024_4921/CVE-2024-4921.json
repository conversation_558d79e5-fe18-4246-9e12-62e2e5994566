{"cve_id": "CVE-2024-4921", "published_date": "2024-05-16T01:15:07.540", "last_modified_date": "2025-02-10T13:23:20.033", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in SourceCodester Employee and Visitor Gate Pass Logging System 1.0. Affected is an unknown function of the file /employee_gatepass/classes/Users.php?f=ssave. The manipulation of the argument img leads to unrestricted upload. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used. The identifier of this vulnerability is VDB-264456."}, {"lang": "es", "value": " Una vulnerabilidad ha sido encontrada en SourceCodester Employee and Visitor Gate Pass Logging System 1.0 y clasificada como crítica. Una función desconocida del archivo /employee_gatepass/classes/Users.php?f=ssave es afectada por esta vulnerabilidad. La manipulación del argumento img conduce a una carga sin restricciones. Es posible lanzar el ataque de forma remota. El exploit ha sido divulgado al público y puede utilizarse. El identificador de esta vulnerabilidad es VDB-264456."}], "references": [{"url": "https://github.com/I-<PERSON><PERSON><PERSON>-I/cev/blob/main/upload.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.264456", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.264456", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.333662", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}