{"cve_id": "CVE-2024-4734", "published_date": "2024-05-15T02:15:11.060", "last_modified_date": "2024-11-21T09:43:28.937", "descriptions": [{"lang": "en", "value": "The Import and export users and customers plugin for WordPress is vulnerable to Stored Cross-Site Scripting via admin settings in all versions up to, and including, ******** due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with administrator-level permissions and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page. This only affects multi-site installations and installations where unfiltered_html has been disabled."}, {"lang": "es", "value": "El complemento The Import and export users and customers para WordPress es vulnerable a Cross-Site Scripting Almacenado a través de la configuración de administrador en todas las versiones hasta la ******** incluida debido a una sanitización de entrada y un escape de salida insuficientes. Esto hace posible que atacantes autenticados, con permisos de nivel de administrador y superiores, inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada. Esto solo afecta a las instalaciones multisitio y a las instalaciones en las que se ha deshabilitado unfiltered_html."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&new=3085346%40import-users-from-csv-with-meta%2Ftrunk&old=3078277%40import-users-from-csv-with-meta%2Ftrunk&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/0dca168f-a383-42fc-91ba-d78a5d7e6724?source=cve", "source": "<EMAIL>", "tags": []}]}