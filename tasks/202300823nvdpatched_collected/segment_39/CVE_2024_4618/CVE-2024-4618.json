{"cve_id": "CVE-2024-4618", "published_date": "2024-05-15T02:15:10.333", "last_modified_date": "2025-01-24T16:37:51.287", "descriptions": [{"lang": "en", "value": "The Exclusive Addons for Elementor plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the Team Member widget in all versions up to, and including, ******* due to insufficient input sanitization and output escaping on user supplied 'url' attribute. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento The Exclusive Addons for Elementor para WordPress son vulnerables a Cross-Site Scripting Almacenado a través del widget de miembro del equipo en todas las versiones hasta la ******* incluida debido a una sanitización de entrada insuficiente y a un escape de salida en el atributo 'url' proporcionado por el usuario. Esto hace posible que atacantes autenticados, con acceso de nivel de colaborador y superior, inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/exclusive-addons-for-elementor/tags/*******/elements/team-member/team-member.php#L1696", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3083582/#file4", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://wordpress.org/plugins/exclusive-addons-for-elementor/#developers", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/2e82478c-e476-4cdf-ab72-f578331058e2?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}