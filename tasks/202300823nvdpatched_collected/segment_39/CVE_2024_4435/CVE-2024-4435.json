{"cve_id": "CVE-2024-4435", "published_date": "2024-05-21T10:15:10.393", "last_modified_date": "2024-11-21T09:42:49.523", "descriptions": [{"lang": "en", "value": "When storing unbounded types in a BTreeMap, a node is represented as a linked list of \"memory chunks\". It was discovered recently that when we deallocate a node, in some cases only the first memory chunk is deallocated, and the rest of the memory chunks remain (incorrectly) allocated, causing a memory leak. In the worst case, depending on how a canister uses the BTreeMap, an adversary could interact with the canister through its API and trigger interactions with the map that keep consuming memory due to the memory leak. This could potentially lead to using an excessive amount of memory, or even running out of memory.\n\nThis issue has been fixed in  #212 https://github.com/dfinity/stable-structures/pull/212  by changing the logic for deallocating nodes to ensure that all of a node's memory chunks are deallocated and users are asked to upgrade to version 0.6.4.. Tests have been added to prevent regressions of this nature moving forward. Note: Users of stable-structure < 0.6.0 are not affected.\n\nUsers who are not storing unbounded types in BTreeMap are not affected and do not need to upgrade. Otherwise, an upgrade to version 0.6.4 is necessary."}, {"lang": "es", "value": "Al almacenar tipos ilimitados en un BTreeMap, un nodo se representa como una lista vinculada de \"fragmentos de memoria\". Recientemente se descubrió que cuando desasignamos un nodo, en algunos casos solo se desasigna el primer fragmento de memoria y el resto de los fragmentos de memoria permanecen (incorrectamente) asignados, lo que provoca una pérdida de memoria. En el peor de los casos, dependiendo de cómo un recipiente utilice BTreeMap, un adversario podría interactuar con el recipiente a través de su API y desencadenar interacciones con el mapa que sigan consumiendo memoria debido a la pérdida de memoria. Esto podría llevar potencialmente al uso de una cantidad excesiva de memoria o incluso a quedarse sin memoria. Este problema se solucionó en el n.º 212 https://github.com/dfinity/stable-structures/pull/212 cambiando la lógica para desasignar nodos para garantizar que todos los fragmentos de memoria de un nodo se desasignen y se solicite a los usuarios que actualicen a versión 0.6.4.. Se han agregado pruebas para evitar que avancen regresiones de esta naturaleza. Nota: Los usuarios de estructura estable &lt;0.6.0 no se ven afectados. Los usuarios que no almacenan tipos ilimitados en BTreeMap no se ven afectados y no necesitan actualizar. De lo contrario, es necesaria una actualización a la versión 0.6.4."}], "references": [{"url": "https://docs.rs/ic-stable-structures/0.6.4/ic_stable_structures/", "source": "6b35d637-e00f-4228-858c-b20ad6e1d07b", "tags": []}, {"url": "https://github.com/dfinity/stable-structures/pull/212", "source": "6b35d637-e00f-4228-858c-b20ad6e1d07b", "tags": []}, {"url": "https://internetcomputer.org/docs/current/developer-docs/smart-contracts/maintain/storage#stable-memory", "source": "6b35d637-e00f-4228-858c-b20ad6e1d07b", "tags": []}]}