{"cve_id": "CVE-2024-4865", "published_date": "2024-05-18T03:15:06.340", "last_modified_date": "2025-01-07T18:04:22.393", "descriptions": [{"lang": "en", "value": "The Happy Addons for Elementor plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘_id’ parameter in all versions up to, and including, 3.10.8 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Happy Addons for Elementor para WordPress es vulnerable a cross site scripting almacenado a través del parámetro '_id' en todas las versiones hasta la 3.10.8 incluida debido a una sanitización de entrada y un escape de salida insuficientes. Esto hace posible que atacantes autenticados, con acceso de nivel de Colaborador y superior, inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/happy-elementor-addons/trunk/widgets/skills/widget.php#L359", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3087575/happy-elementor-addons/trunk/widgets/skills/widget.php", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/2fdf2020-ad80-44c3-89b6-fc2ba067cd33?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}