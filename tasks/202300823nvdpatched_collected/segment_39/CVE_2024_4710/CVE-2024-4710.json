{"cve_id": "CVE-2024-4710", "published_date": "2024-05-21T07:15:09.087", "last_modified_date": "2024-11-21T09:43:25.600", "descriptions": [{"lang": "en", "value": "The UberMenu plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's ubermenu-col, ubermenu_mobile_close_button, ubermenu_toggle, ubermenu-search shortcodes in all versions up to, and including, 3.8.2 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento UberMenu para WordPress es vulnerable a Cross Site Scripting Almacenado a través de los códigos cortos ubermenu-col, ubermenu_mobile_close_button, ubermenu_toggle y ubermenu-search del complemento en todas las versiones hasta la 3.8.2 incluida debido a una sanitización de entrada insuficiente y a que la salida se escape al usuario. atributos proporcionados. Esto hace posible que atacantes autenticados, con acceso de nivel de colaborador y superior, inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://codecanyon.net/item/ubermenu-wordpress-mega-menu-plugin/154703", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/*************-49f7-91ab-9ad05b900a81?source=cve", "source": "<EMAIL>", "tags": []}]}