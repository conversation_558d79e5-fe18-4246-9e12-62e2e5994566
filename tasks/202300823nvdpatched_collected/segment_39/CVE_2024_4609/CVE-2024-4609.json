{"cve_id": "CVE-2024-4609", "published_date": "2024-05-16T16:15:10.750", "last_modified_date": "2025-01-30T15:50:28.557", "descriptions": [{"lang": "en", "value": "A vulnerability exists in the Rockwell Automation FactoryTalk® View SE Datalog function that could allow a threat actor to inject a malicious SQL statement if the SQL database has no authentication in place or if legitimate credentials were stolen. If exploited, the attack could result in information exposure, revealing sensitive information. Additionally, a threat actor could potentially modify and delete the data in a remote database. An attack would only affect the HMI design time, not runtime."}, {"lang": "es", "value": " Existe una vulnerabilidad en la función FactoryTalk® View SE Datalog de Rockwell Automation que podría permitir que un actor malicioso inyecte una declaración SQL maliciosa si la base de datos SQL no tiene autenticación implementada o si se robaron credenciales legítimas. Si se explota, el ataque podría dar como resultado la exposición de información, revelando información confidencial. Además, un actor malicioso podría modificar y eliminar los datos de una base de datos remota. Un ataque sólo afectaría el tiempo de diseño de la HMI, no el tiempo de ejecución."}], "references": [{"url": "https://www.rockwellautomation.com/en-us/support/advisory.SD1670.html", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}