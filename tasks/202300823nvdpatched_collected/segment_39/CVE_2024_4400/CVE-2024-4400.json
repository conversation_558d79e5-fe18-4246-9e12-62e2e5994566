{"cve_id": "CVE-2024-4400", "published_date": "2024-05-16T11:15:48.773", "last_modified_date": "2025-03-20T20:10:54.077", "descriptions": [{"lang": "en", "value": "The Post and Page Builder by BoldGrid – Visual Drag and Drop Editor plguin for WordPress is vulnerable to Stored Cross-Site Scripting via an unknown parameter in versions up to, and including, 1.26.4 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with contributor-level permissions and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Post and Page Builder de BoldGrid – Visual Drag and Drop Editor para WordPress es vulnerable a Cross-Site Scripting Almacenado a través de un parámetro desconocido en versiones hasta la 1.26.4 incluida, debido a una desinfección de entrada y un escape de salida insuficientes. Esto permite que atacantes autenticados, con permisos de nivel de colaborador y superiores, inyecten secuencias de comandos web arbitrarias en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset/3087230/", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/9bb6683a-b8e6-4776-880f-5b48966fc5c6?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}