{"cve_id": "CVE-2024-4932", "published_date": "2024-05-16T05:15:51.653", "last_modified_date": "2024-12-09T22:50:41.213", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in SourceCodester Simple Online Bidding System 1.0. Affected is an unknown function of the file /simple-online-bidding-system/admin/index.php?page=manage_user. The manipulation of the argument id leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used. The identifier of this vulnerability is VDB-264468."}, {"lang": "es", "value": " Una vulnerabilidad fue encontrada en SourceCodester Simple Online Bidding System 1.0 y clasificada como crítica. Una función desconocida del archivo /simple-online-bidding-system/admin/index.php?page=manage_user es afectada por esta vulnerabilidad. La manipulación del argumento id conduce a la inyección de SQL. Es posible lanzar el ataque de forma remota. El exploit ha sido divulgado al público y puede utilizarse. El identificador de esta vulnerabilidad es VDB-264468."}], "references": [{"url": "https://github.com/rockersiyuan/CVE/blob/main/SourceCodester%20Simple%20Online%20Bidding%20System%20Sql%20Inject-3.md", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.264468", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.264468", "source": "<EMAIL>", "tags": ["VDB Entry"]}, {"url": "https://vuldb.com/?submit.335366", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}