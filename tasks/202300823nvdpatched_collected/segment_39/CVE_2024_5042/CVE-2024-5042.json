{"cve_id": "CVE-2024-5042", "published_date": "2024-05-17T14:15:21.123", "last_modified_date": "2024-11-21T09:46:50.500", "descriptions": [{"lang": "en", "value": "A flaw was found in the Submariner project. Due to unnecessary role-based access control permissions, a privileged attacker can run a malicious container on a node that may allow them to steal service account tokens and further compromise other nodes and potentially the entire cluster."}, {"lang": "es", "value": "Se encontró un fallo en el proyecto Submariner. Debido a permisos innecesarios de control de acceso basados en roles, un atacante privilegiado puede ejecutar un contenedor malicioso en un nodo que puede permitirle robar tokens de cuentas de servicio y comprometer aún más otros nodos y potencialmente todo el clúster."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2024:4591", "source": "<EMAIL>", "tags": []}, {"url": "https://access.redhat.com/security/cve/CVE-2024-5042", "source": "<EMAIL>", "tags": []}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2280921", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/advisories/GHSA-2rhx-qhxp-5jpw", "source": "<EMAIL>", "tags": []}]}