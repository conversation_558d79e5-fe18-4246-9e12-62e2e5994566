{"cve_id": "CVE-2024-5066", "published_date": "2024-05-17T20:15:07.597", "last_modified_date": "2025-03-03T16:13:18.990", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in PHPGurukul Online Course Registration System 3.1. Affected by this vulnerability is an unknown functionality of the file /pincode-verification.php. The manipulation of the argument pincode leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used. The identifier VDB-264925 was assigned to this vulnerability."}, {"lang": "es", "value": " Una vulnerabilidad fue encontrada en PHPGurukul Online Course Registration System 3.1 y clasificada como crítica. Una función desconocida del archivo /pincode-verification.php es afectada por esta vulnerabilidad. La manipulación del argumento PINcode conduce a la inyección SQL. El ataque se puede lanzar de forma remota. El exploit ha sido divulgado al público y puede utilizarse. A esta vulnerabilidad se le asignó el identificador VDB-264925."}], "references": [{"url": "https://github.com/BurakSevben/CVEs/blob/main/Online%20Course%20Registration%20System/Online%20Course%20Registration%20System%20-%20SQL%20Injection%20-%204.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.264925", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.264925", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.336240", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}