{"cve_id": "CVE-2024-5101", "published_date": "2024-05-19T14:15:35.700", "last_modified_date": "2025-02-10T14:25:59.993", "descriptions": [{"lang": "en", "value": "A vulnerability was found in SourceCodester Simple Inventory System 1.0. It has been declared as critical. This vulnerability affects unknown code of the file updateproduct.php. The manipulation of the argument ITEM leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used. The identifier of this vulnerability is VDB-265084."}, {"lang": "es", "value": " Se encontró una vulnerabilidad en SourceCodester Simple Inventory System 1.0. Ha sido declarada crítica. Esta vulnerabilidad afecta a un código desconocido del archivo updateproduct.php. La manipulación del argumento ITEM conduce a la inyección SQL. El ataque se puede iniciar de forma remota. El exploit ha sido divulgado al público y puede utilizarse. El identificador de esta vulnerabilidad es VDB-265084."}], "references": [{"url": "https://github.com/rockersiyuan/CVE/blob/main/SourceCodester%20Simple%20Inventory%20System%20Sql%20Inject-4.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.265084", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.265084", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.337059", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}