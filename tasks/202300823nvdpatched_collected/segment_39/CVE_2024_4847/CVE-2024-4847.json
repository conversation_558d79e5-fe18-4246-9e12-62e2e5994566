{"cve_id": "CVE-2024-4847", "published_date": "2024-05-15T02:15:11.330", "last_modified_date": "2024-11-21T09:43:43.737", "descriptions": [{"lang": "en", "value": "The Alt Text AI – Automatically generate image alt text for SEO and accessibility plugin for WordPress is vulnerable to generic SQL Injection via the ‘last_post_id’ parameter in all versions up to, and including, 1.4.9 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for authenticated attackers, with Subscriber-level access and above, to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento The Alt Text AI – Automatically generate image alt text for SEO and accessibility para WordPress es vulnerable a la inyección SQL genérica a través del parámetro 'last_post_id' en todas las versiones hasta la 1.4.9 incluida debido a un escape insuficiente en el parámetro proporcionado por el usuario. y falta de preparación suficiente en la consulta SQL existente. Esto hace posible que los atacantes autenticados, con acceso a nivel de suscriptor y superior, agreguen consultas SQL adicionales a consultas ya existentes que pueden usarse para extraer información confidencial de la base de datos."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/alttext-ai/trunk/includes/class-atai-attachment.php#L677", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3086107/", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/alttext-ai/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/3c192623-eb46-4f1d-b897-433ac80608cb?source=cve", "source": "<EMAIL>", "tags": []}]}