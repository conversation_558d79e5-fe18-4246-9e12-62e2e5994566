{"cve_id": "CVE-2024-5122", "published_date": "2024-05-20T07:15:09.637", "last_modified_date": "2025-02-10T14:31:42.370", "descriptions": [{"lang": "en", "value": "A vulnerability was found in SourceCodester Event Registration System 1.0. It has been rated as critical. Affected by this issue is some unknown functionality of the file /registrar/. The manipulation of the argument search leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used. VDB-265202 is the identifier assigned to this vulnerability."}, {"lang": "es", "value": " Se encontró una vulnerabilidad en SourceCodester Event Registration System 1.0. Ha sido calificada como crítica. Una función desconocida del archivo /registrar/ es afectada por esta vulnerabilidad. La manipulación del argumento search  conduce a la inyección de SQL. El ataque puede lanzarse de forma remota. El exploit ha sido divulgado al público y puede utilizarse. VDB-265202 es el identificador asignado a esta vulnerabilidad."}], "references": [{"url": "https://github.com/BurakSevben/CVEs/blob/main/Event%20Registration%20System/Event%20Registration%20System%20-%20SQL%20Injection%20-%204.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.265202", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.265202", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.338615", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}