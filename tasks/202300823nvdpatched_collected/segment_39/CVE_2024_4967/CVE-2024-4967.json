{"cve_id": "CVE-2024-4967", "published_date": "2024-05-16T09:15:17.617", "last_modified_date": "2025-02-10T13:42:20.800", "descriptions": [{"lang": "en", "value": "A vulnerability was found in SourceCodester Interactive Map with Marker 1.0. It has been declared as critical. Affected by this vulnerability is an unknown functionality of the file /endpoint/delete-mark.php. The manipulation of the argument mark leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used. The associated identifier of this vulnerability is VDB-264535."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en SourceCodester Interactive Map con Marker 1.0. Ha sido declarada crítica. Una función desconocida del archivo /endpoint/delete-mark.php es afectada por esta vulnerabilidad. La manipulación del argumento mark conduce a la inyección de SQL. El ataque se puede lanzar de forma remota. El exploit ha sido divulgado al público y puede utilizarse. El identificador asociado de esta vulnerabilidad es VDB-264535."}], "references": [{"url": "https://github.com/BurakSevben/CVEs/blob/main/Interactive%20Map%20App/Interactive%20Map%20App%20-%20SQL%20Injection.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.264535", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.264535", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.335190", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}