{"cve_id": "CVE-2024-4909", "published_date": "2024-05-15T19:15:09.320", "last_modified_date": "2025-02-20T21:20:22.503", "descriptions": [{"lang": "en", "value": "A vulnerability was found in Campcodes Complete Web-Based School Management System 1.0. It has been classified as critical. Affected is an unknown function of the file /view/student_due_payment.php. The manipulation of the argument due_year leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used. The identifier of this vulnerability is VDB-264444."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en Campcodes Complete Web-Based School Management System 1.0. Ha sido clasificada como crítica. Una función desconocida del archivo /view/student_due_paid.php es afectada por esta vulnerabilidad. La manipulación del argumento debido_año conduce a la inyección SQL. Es posible lanzar el ataque de forma remota. El exploit ha sido divulgado al público y puede utilizarse. El identificador de esta vulnerabilidad es VDB-264444."}], "references": [{"url": "https://github.com/E1CHO/cve_hub/blob/main/Complete%20Web-Based%20School%20Management%20System%20-%20sql/Complete%20Web-Based%20School%20Management%20System%20-%20vuln%205.pdf", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.264444", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.264444", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.333295", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}