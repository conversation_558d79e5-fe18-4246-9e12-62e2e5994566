{"cve_id": "CVE-2024-4844", "published_date": "2024-05-16T07:15:50.743", "last_modified_date": "2024-11-21T09:43:43.280", "descriptions": [{"lang": "en", "value": "Hardcoded credentials vulnerability in Trellix ePolicy Orchestrator (ePO) on Premise prior to 5.10 Service Pack 1 Update 2 allows an attacker with admin privileges on the ePO server to read the contents of the orion.keystore file, allowing them to access the ePO database encryption key.  This was possible through using a hard coded password for the keystore.  Access Control restrictions on the file mean this would not be exploitable unless the user is the system admin for the server that ePO is running on."}, {"lang": "es", "value": "La vulnerabilidad de credenciales codificadas de forma rígida en Trellix ePolicy Orchestrator (ePO) on Premise anterior a la versión 5.10 Service Pack 1 Update 2 permite a un atacante con privilegios de administrador en el servidor ePO leer el contenido del archivo orion.keystore, lo que le permite acceder a la clave de cifrado de la base de datos de ePO. Esto era posible mediante el uso de una contraseña codificada de forma rígida para el almacén de claves. Las restricciones de control de acceso en el archivo significan que esto no sería explotable a menos que el usuario sea el administrador del sistema del servidor en el que se ejecuta ePO."}], "references": [{"url": "https://thrive.trellix.com/s/article/000013505", "source": "<EMAIL>", "tags": []}]}