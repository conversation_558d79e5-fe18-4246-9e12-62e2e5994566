{"cve_id": "CVE-2024-4923", "published_date": "2024-05-16T02:15:07.900", "last_modified_date": "2025-03-03T15:58:25.597", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in Codezips E-Commerce Site 1.0 and classified as critical. This vulnerability affects unknown code of the file admin/addproduct.php. The manipulation of the argument profilepic leads to unrestricted upload. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used. The identifier of this vulnerability is VDB-264460."}, {"lang": "es", "value": " Una vulnerabilidad fue encontrada en Codezips E-Commerce Site 1.0 y clasificada como crítica. Esta vulnerabilidad afecta a un código desconocido del archivo admin/addproduct.php. La manipulación del argumento profilepìc conduce a una carga sin restricciones. El ataque se puede iniciar de forma remota. El exploit ha sido divulgado al público y puede utilizarse. El identificador de esta vulnerabilidad es VDB-264460."}], "references": [{"url": "https://github.com/polaris0x1/CVE/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.264460", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.264460", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.333874", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}