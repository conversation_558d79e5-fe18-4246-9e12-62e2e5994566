{"cve_id": "CVE-2024-4709", "published_date": "2024-05-18T08:15:08.410", "last_modified_date": "2025-02-06T18:37:12.683", "descriptions": [{"lang": "en", "value": "The Contact Form Plugin by Fluent Forms for Quiz, Survey, and Drag & Drop WP Form Builder plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘subject’ parameter in versions up to, and including, 5.1.16 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with contributor-level permissions and above, and access granted by an administrator, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Contact Form Plugin by Fluent Forms for Quiz, Survey, and Drag &amp; Drop WP Form Builder para WordPress es vulnerable a cross site scripting almacenado a través del parámetro 'subject' en versiones hasta la 5.1.16 incluida debido a una sanitización de entrada insuficiente y escape de salida. Esto hace posible que atacantes autenticados, con permisos de nivel de colaborador y superiores, y acceso otorgado por un administrador, inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/fluentform/trunk/app/Services/FormBuilder/Notifications/EmailNotification.php#L106", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/fluentform/trunk/app/Services/FormBuilder/Notifications/EmailNotification.php#L164", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/fluentform/trunk/app/Services/FormBuilder/Notifications/EmailNotification.php#L194", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3088078/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/5fe317a6-a391-441a-aac8-c8fa57e73169?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}