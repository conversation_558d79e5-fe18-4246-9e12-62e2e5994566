{"cve_id": "CVE-2024-4452", "published_date": "2024-05-21T14:15:12.563", "last_modified_date": "2025-01-09T17:46:34.190", "descriptions": [{"lang": "en", "value": "The ElementsKit Pro plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘url’ parameter in versions up to, and including, 3.6.1 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with contributor-level permissions and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": " El complemento ElementsKit Pro para WordPress es vulnerable a Cross Site Scripting Almacenado a través del parámetro 'url' en versiones hasta la 3.6.1 incluida debido a una sanitización de entrada y un escape de salida insuficientes. Esto hace posible que atacantes autenticados, con permisos de nivel de colaborador y superiores, inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://wpmet.com/plugin/elementskit/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/488ac848-786e-4100-a387-5a40e8fc4175?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}