{"cve_id": "CVE-2024-5023", "published_date": "2024-05-16T18:15:10.767", "last_modified_date": "2024-11-21T09:46:48.187", "descriptions": [{"lang": "en", "value": "Improper Neutralization of Special Elements used in a Command ('Command Injection') vulnerability in Netflix ConsoleMe allows Command Injection.This issue affects ConsoleMe: before 1.4.0."}, {"lang": "es", "value": "La neutralización incorrecta de elementos especiales utilizados en una vulnerabilidad de comando (\"Inyección de comando\") en Netflix ConsoleMe permite la inyección de comando. Este problema afecta a ConsoleMe: versiones anteriores a 1.4.0."}], "references": [{"url": "https://github.com/Netflix/security-bulletins/blob/master/advisories/nflx-2024-002.md", "source": "<EMAIL>", "tags": []}]}