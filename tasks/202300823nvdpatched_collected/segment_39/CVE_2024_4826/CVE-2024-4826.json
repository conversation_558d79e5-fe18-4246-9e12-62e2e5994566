{"cve_id": "CVE-2024-4826", "published_date": "2024-05-16T12:15:14.330", "last_modified_date": "2024-11-21T09:43:42.160", "descriptions": [{"lang": "en", "value": "SQL injection vulnerability in Simple PHP Shopping Cart affecting version 0.9. This vulnerability could allow an attacker to retrieve all the information stored in the database by sending a specially crafted SQL query, due to the lack of proper sanitisation of the category_id parameter in the category.php file."}, {"lang": "es", "value": "Vulnerabilidad de inyección SQL en Simple PHP Shopping Cart que afecta a la versión 0.9. Esta vulnerabilidad podría permitir a un atacante recuperar toda la información almacenada en la base de datos mediante el envío de una consulta SQL especialmente manipulada, debido a la falta de una correcta desinfección del parámetro category_id en el archivo category.php."}], "references": [{"url": "https://www.incibe.es/en/incibe-cert/notices/aviso/multiple-vulnerabilities-simple-php-shopping-cart", "source": "<EMAIL>", "tags": []}]}