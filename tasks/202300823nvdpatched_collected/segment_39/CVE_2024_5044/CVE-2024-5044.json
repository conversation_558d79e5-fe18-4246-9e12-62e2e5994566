{"cve_id": "CVE-2024-5044", "published_date": "2024-05-17T12:15:17.727", "last_modified_date": "2025-03-05T18:42:24.920", "descriptions": [{"lang": "en", "value": "A vulnerability was found in Emlog Pro 2.3.4. It has been classified as problematic. This affects an unknown part of the component <PERSON><PERSON>. The manipulation of the argument AuthCookie leads to improper authentication. It is possible to initiate the attack remotely. The complexity of an attack is rather high. The exploitability is told to be difficult. The exploit has been disclosed to the public and may be used. The identifier VDB-264741 was assigned to this vulnerability. NOTE: The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en Emlog Pro 2.3.4. Ha sido clasificada como problemática. Una parte desconocida del componente Cookie Handler afecta a una parte desconocida. La manipulación del argumento AuthCookie conduce a una autenticación incorrecta. Es posible iniciar el ataque de forma remota. La complejidad de un ataque es bastante alta. Se dice que la explotabilidad es difícil. El exploit ha sido divulgado al público y puede utilizarse. A esta vulnerabilidad se le asignó el identificador VDB-264741. NOTA: Se contactó primeramente con el proveedor sobre esta divulgación, pero no respondió de ninguna manera."}], "references": [{"url": "https://github.com/ssteveez/emlog/blob/main/emlog%20pro%20version%202.3.4%20has%20session(AuthCookie)%20persistence%20and%20any%20user%20login%20vulnerability.md", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.264741", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.264741", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.331857", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}