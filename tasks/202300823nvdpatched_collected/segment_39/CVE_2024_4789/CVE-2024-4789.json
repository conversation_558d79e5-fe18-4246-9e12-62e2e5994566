{"cve_id": "CVE-2024-4789", "published_date": "2024-05-17T09:15:45.300", "last_modified_date": "2024-11-21T09:43:36.877", "descriptions": [{"lang": "en", "value": "Cost Calculator Builder Pro plugin for WordPress is vulnerable to Server-Side Request Forgery in all versions up to 3.1.72, via the send_demo_webhook() function. This makes it possible for authenticated attackers, with subscriber-level access and above, to make web requests to arbitrary locations originating from the web application and can be used to query and modify information from internal services."}, {"lang": "es", "value": "El complemento Cost Calculator Builder Pro para WordPress es vulnerable a Server-Side Request Forgery en todas las versiones hasta la 3.1.72, a través de la función send_demo_webhook(). Esto hace posible que atacantes autenticados, con acceso de nivel de suscriptor y superior, realicen solicitudes web a ubicaciones arbitrarias que se originan en la aplicación web y pueden usarse para consultar y modificar información de servicios internos."}], "references": [{"url": "https://stylemixthemes.com/cost-calculator-plugin/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/c6840350-7ff4-4ec2-bf2b-94ce6f782537?source=cve", "source": "<EMAIL>", "tags": []}]}