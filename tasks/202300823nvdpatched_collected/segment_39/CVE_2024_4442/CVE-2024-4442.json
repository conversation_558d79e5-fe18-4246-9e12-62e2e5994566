{"cve_id": "CVE-2024-4442", "published_date": "2024-05-21T07:15:08.460", "last_modified_date": "2025-04-18T16:08:37.073", "descriptions": [{"lang": "en", "value": "The Salon booking system plugin for WordPress is vulnerable to arbitrary file deletion in all versions up to, and including, 9.8. This is due to the plugin not properly validating the path of an uploaded file prior to deleting it. This makes it possible for unauthenticated attackers to delete arbitrary files, including the wp-config.php file, which can make site takeover and remote code execution possible."}, {"lang": "es", "value": " El complemento Salon booking system para WordPress es vulnerable a la eliminación arbitraria de archivos en todas las versiones hasta la 9.8 incluida. Esto se debe a que el complemento no valida correctamente la ruta de un archivo cargado antes de eliminarlo. Esto hace posible que atacantes no autenticados eliminen archivos arbitrarios, incluido el archivo wp-config.php, lo que puede hacer posible la toma de control del sitio y la ejecución remota de código."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/salon-booking-system/tags/9.8/src/SLN/Action/Ajax/RemoveUploadedFile.php#L5", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3088196/salon-booking-system#file14", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/eaafeadd-f44c-49b1-b900-ef40800c629e?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}