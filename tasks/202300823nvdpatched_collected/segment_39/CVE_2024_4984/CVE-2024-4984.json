{"cve_id": "CVE-2024-4984", "published_date": "2024-05-16T02:15:09.003", "last_modified_date": "2024-11-21T09:44:00.193", "descriptions": [{"lang": "en", "value": "The Yoast SEO plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘display_name’ author meta in all versions up to, and including, 22.6 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Yoast SEO para WordPress es vulnerable a Cross-Site Scripting Almacenado a través del metadato del autor 'display_name' en todas las versiones hasta la 22.6 incluida, debido a una desinfección de entrada y un escape de salida insuficientes. Esto permite que atacantes autenticados, con acceso de nivel de colaborador y superior, inyecten secuencias de comandos web arbitrarias en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://developer.yoast.com/changelog/yoast-seo/22.7/", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/Yoast/wordpress-seo/pull/21334", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3079234/wordpress-seo/trunk/src/presenters/slack/enhanced-data-presenter.php", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/59bcd246-ca2f-4336-9a6e-89afe873ed25?source=cve", "source": "<EMAIL>", "tags": []}]}