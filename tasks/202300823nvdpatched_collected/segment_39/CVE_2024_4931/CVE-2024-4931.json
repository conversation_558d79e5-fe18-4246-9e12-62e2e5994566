{"cve_id": "CVE-2024-4931", "published_date": "2024-05-16T05:15:51.297", "last_modified_date": "2024-12-09T22:50:24.617", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in SourceCodester Simple Online Bidding System 1.0. This issue affects some unknown processing of the file /simple-online-bidding-system/admin/index.php?page=view_udet. The manipulation of the argument id leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used. The associated identifier of this vulnerability is VDB-264467."}, {"lang": "es", "value": "Una vulnerabilidad fue encontrada en SourceCodester Simple Online Bidding System 1.0 y clasificada como crítica. Este problema afecta un procesamiento desconocido del archivo /simple-online-bidding-system/admin/index.php?page=view_udet. La manipulación del argumento id conduce a la inyección de SQL. El ataque puede iniciarse de forma remota. El exploit ha sido divulgado al público y puede utilizarse. El identificador asociado de esta vulnerabilidad es VDB-264467."}], "references": [{"url": "https://github.com/rockersiyuan/CVE/blob/main/SourceCodester%20Simple%20Online%20Bidding%20System%20Sql%20Inject-2.md", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.264467", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.264467", "source": "<EMAIL>", "tags": ["VDB Entry"]}, {"url": "https://vuldb.com/?submit.335365", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}