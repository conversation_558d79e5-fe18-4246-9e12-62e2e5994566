{"cve_id": "CVE-2024-5043", "published_date": "2024-05-17T12:15:16.650", "last_modified_date": "2025-03-05T18:41:15.673", "descriptions": [{"lang": "en", "value": "A vulnerability was found in Emlog Pro 2.3.4 and classified as critical. Affected by this issue is some unknown functionality of the file admin/setting.php. The manipulation leads to unrestricted upload. The attack may be launched remotely. The exploit has been disclosed to the public and may be used. The identifier of this vulnerability is VDB-264740. NOTE: The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": " Una vulnerabilidad fue encontrada en Emlog Pro 2.3.4 y clasificada como crítica. Una función desconocida del archivo admin/setting.php es afectada por esta vulnerabilidad. La manipulación conduce a una carga sin restricciones. El ataque puede lanzarse de forma remota. El exploit ha sido divulgado al público y puede utilizarse. El identificador de esta vulnerabilidad es VDB-264740. NOTA: Se contactó primeramente con el proveedor sobre esta divulgación, pero no respondió de ninguna manera."}], "references": [{"url": "https://github.com/ssteveez/emlog/blob/main/emlog%20pro%20version%202.3.4%20Admin%20side%20can%20upload%20arbitrary%20files%20and%20getshell.md", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.264740", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.264740", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.331854", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}