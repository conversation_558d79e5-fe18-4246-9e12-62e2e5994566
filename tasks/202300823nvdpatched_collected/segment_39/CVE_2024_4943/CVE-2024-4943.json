{"cve_id": "CVE-2024-4943", "published_date": "2024-05-21T03:15:08.540", "last_modified_date": "2025-02-03T18:22:31.240", "descriptions": [{"lang": "en", "value": "The Blocksy theme for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘has_field_link_rel’ parameter in all versions up to, and including, 2.0.46 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": " El tema Blocksy para WordPress es vulnerable a Cross Site Scripting Almacenado a través del parámetro 'has_field_link_rel' en todas las versiones hasta la 2.0.46 incluida debido a una sanitización de entrada y un escape de salida insuficientes. Esto hace posible que atacantes autenticados, con acceso de nivel de Colaborador y superior, inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://themes.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&new=227333%40blocksy%2F2.0.47&old=227242%40blocksy%2F2.0.46", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/dc7099d7-94fd-42be-a921-bfcad43ae252?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}