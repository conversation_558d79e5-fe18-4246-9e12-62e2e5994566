{"cve_id": "CVE-2024-4928", "published_date": "2024-05-16T03:15:08.387", "last_modified_date": "2024-12-09T22:45:35.050", "descriptions": [{"lang": "en", "value": "A vulnerability was found in SourceCodester Simple Online Bidding System 1.0. It has been rated as critical. Affected by this issue is some unknown functionality of the file /simple-online-bidding-system/admin/ajax.php?action=delete_category. The manipulation of the argument id leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used. The identifier of this vulnerability is VDB-264464."}, {"lang": "es", "value": " Se encontró una vulnerabilidad en SourceCodester Simple Online Bidding System 1.0. Ha sido calificada como crítica. Una función desconocida del archivo /simple-online-bidding-system/admin/ajax.php?action=delete_category es afectada por esta vulnerabilidad. La manipulación del argumento id conduce a la inyección de SQL. El ataque puede lanzarse de forma remota. El exploit ha sido divulgado al público y puede utilizarse. El identificador de esta vulnerabilidad es VDB-264464."}], "references": [{"url": "https://github.com/Hefei-Coffee/cve/blob/main/sql8.md", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.264464", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.264464", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.333893", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}