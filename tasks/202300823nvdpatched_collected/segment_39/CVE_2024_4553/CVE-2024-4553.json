{"cve_id": "CVE-2024-4553", "published_date": "2024-05-21T10:15:10.623", "last_modified_date": "2025-01-24T14:42:56.390", "descriptions": [{"lang": "en", "value": "The WP Shortcodes Plugin — Shortcodes Ultimate plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's 'su_members' shortcode in all versions up to, and including, 7.1.5 due to insufficient input sanitization and output escaping on user supplied 'color' attribute. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento WP Shortcodes Plugin — Shortcodes Ultimate para WordPress es vulnerable a Cross Site Scripting Almacenado a través del código abreviado 'su_members' del complemento en todas las versiones hasta la 7.1.5 incluida debido a una sanitización de entrada insuficiente y a que la salida se escapa en el atributo 'color' proporcionado por el usuario. Esto hace posible que atacantes autenticados, con acceso de nivel de colaborador y superior, inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/shortcodes-ultimate/tags/7.1.4/includes/shortcodes/members.php#L83", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3084162/#file524", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/d8db8ed5-ebeb-4102-928f-fe417e429ad2?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}