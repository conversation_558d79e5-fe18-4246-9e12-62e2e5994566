{"cve_id": "CVE-2024-5135", "published_date": "2024-05-20T09:15:09.750", "last_modified_date": "2025-02-21T20:44:01.817", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Directory Management System 1.0. It has been rated as critical. This issue affects some unknown processing of the file /admin/index.php. The manipulation of the argument username leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used. The associated identifier of this vulnerability is VDB-265211."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Directory Management System 1.0. Ha sido calificada como crítica. Este problema afecta un procesamiento desconocido del archivo /admin/index.php. La manipulación del argumento username conduce a la inyección de SQL. El ataque puede iniciarse de forma remota. El exploit ha sido divulgado al público y puede utilizarse. El identificador asociado de esta vulnerabilidad es VDB-265211."}], "references": [{"url": "https://github.com/BurakSevben/CVEs/blob/main/Directory%20Management%20System/Directory%20Management%20System%20-%20SQL%20Injection%20-%201.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.265211", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.265211", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.339121", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}