{"cve_id": "CVE-2024-4945", "published_date": "2024-05-16T05:15:52.187", "last_modified_date": "2025-02-10T13:34:57.043", "descriptions": [{"lang": "en", "value": "A vulnerability was found in SourceCodester Best Courier Management System 1.0. It has been classified as problematic. Affected is an unknown function of the file view_parcel.php. The manipulation of the argument id leads to unrestricted upload. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used. The identifier of this vulnerability is VDB-264480."}, {"lang": "es", "value": " Se encontró una vulnerabilidad en SourceCodester Best Courier Management System 1.0. Ha sido clasificada como problemática. Una función desconocida del archivo view_parcel.php es afectada por esta vulnerabilidad. La manipulación del argumento id conduce a una carga sin restricciones. Es posible lanzar el ataque de forma remota. El exploit ha sido divulgado al público y puede utilizarse. El identificador de esta vulnerabilidad es VDB-264480."}], "references": [{"url": "https://github.com/CveSecLook/cve/issues/28", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.264480", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.264480", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.333960", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}