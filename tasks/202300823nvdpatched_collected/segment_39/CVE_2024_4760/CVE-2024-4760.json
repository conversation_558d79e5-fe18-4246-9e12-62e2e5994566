{"cve_id": "CVE-2024-4760", "published_date": "2024-05-16T13:15:47.893", "last_modified_date": "2025-06-06T15:15:22.837", "descriptions": [{"lang": "en", "value": "A voltage glitch during the startup of EEFC NVM controllers on Microchip SAM E70/S70/V70/V71, SAM G55, SAM 4C/4S/4N/4E, and SAM 3S/3N/3U microcontrollers allows access to the memory bus via the debug interface even if the security bit is set."}, {"lang": "es", "value": "Una falla de voltaje durante el inicio de los controladores EEFC NVM en los microcontroladores Microchip SAM E70/S70/V70/V71 permite el acceso al bus de memoria a través de la interfaz de depuración incluso si el bit de seguridad está configurado."}], "references": [{"url": "https://ww1.microchip.com/downloads/aemDocuments/documents/MCU32/ProductDocuments/SupportingCollateral/Security-Advisory-CVE-2024-4760.pdf", "source": "dc3f6da9-85b5-4a73-84a2-2ec90b40fca5", "tags": []}, {"url": "https://www.0x01team.com/hw_security/bypassing-microchip-atmel-sam-e70-s70-v70-v71-security/", "source": "dc3f6da9-85b5-4a73-84a2-2ec90b40fca5", "tags": []}]}