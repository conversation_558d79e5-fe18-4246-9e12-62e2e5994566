{"cve_id": "CVE-2024-4973", "published_date": "2024-05-16T10:15:10.973", "last_modified_date": "2025-02-18T18:41:10.670", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in code-projects Simple Chat System 1.0. This vulnerability affects unknown code of the file /register.php. The manipulation of the argument name/number/address leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used. VDB-264538 is the identifier assigned to this vulnerability."}, {"lang": "es", "value": " Una vulnerabilidad fue encontrada en el proyecto de código Simple Chat System 1.0 y clasificada como crítica. Esta vulnerabilidad afecta a un código desconocido del archivo /register.php. La manipulación del argumento name/number/address conduce a la inyección SQL. El ataque se puede iniciar de forma remota. El exploit ha sido divulgado al público y puede utilizarse. VDB-264538 es el identificador asignado a esta vulnerabilidad."}], "references": [{"url": "https://github.com/BurakSevben/CVEs/blob/main/Simple%20Chat%20App/Simple%20Chat%20App%20-%20SQL%20Injection%20-%202.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.264538", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.264538", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.335200", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}