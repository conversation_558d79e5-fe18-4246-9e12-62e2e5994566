{"cve_id": "CVE-2024-4702", "published_date": "2024-05-15T12:15:08.147", "last_modified_date": "2025-02-03T16:28:43.907", "descriptions": [{"lang": "en", "value": "The Mega Elements plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the plugin's Button widget in all versions up to, and including, 1.2.1 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers, with contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Mega Elements para WordPress es vulnerable a Cross-Site Scripting Almacenado a través del widget Button del complemento en todas las versiones hasta la 1.2.1 incluida, debido a una desinfección de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite que atacantes autenticados, con acceso de nivel de colaborador y superior, inyecten secuencias de comandos web arbitrarias en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset/3085457/mega-elements-addons-for-elementor", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/3808ca2a-e78e-4118-890b-c22a71f8e855?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}