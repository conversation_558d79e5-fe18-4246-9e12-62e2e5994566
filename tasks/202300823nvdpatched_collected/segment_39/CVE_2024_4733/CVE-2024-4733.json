{"cve_id": "CVE-2024-4733", "published_date": "2024-05-16T20:15:09.993", "last_modified_date": "2024-11-21T09:43:28.800", "descriptions": [{"lang": "en", "value": "The ShiftController Employee Shift Scheduling plugin is vulnerable to PHP Object Injection via deserialization of untrusted input via the `hc3_session`-cookie in versions up to, and including, 4.9.57. This makes it possible for an authenticated attacker with contributor access-level or above to inject a PHP Object. No POP chain is present in the vulnerable plugin. If a POP chain is present via an additional plugin or theme installed on the target system, it could allow the attacker to delete arbitrary files, retrieve sensitive data, or execute code."}, {"lang": "es", "value": "El complemento ShiftController Employee Shift Scheduling es vulnerable a la inyección de objetos PHP mediante la deserialización de entradas que no son de confianza a través de la cookie `hc3_session` en versiones hasta la 4.9.57 inclusive. Esto hace posible que un atacante autenticado con nivel de acceso de colaborador o superior inyecte un objeto PHP. No hay ninguna cadena POP presente en el complemento vulnerable. Si hay una cadena POP presente a través de un complemento o tema adicional instalado en el sistema de destino, podría permitir al atacante eliminar archivos arbitrarios, recuperar datos confidenciales o ejecutar código."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&new=3087047%40shiftcontroller%2Ftrunk&old=3080165%40shiftcontroller%2Ftrunk&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/9c8ab916-240d-43c3-92d4-7efd75862a5e?source=cve", "source": "<EMAIL>", "tags": []}]}