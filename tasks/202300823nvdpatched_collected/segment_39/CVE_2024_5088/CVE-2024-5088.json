{"cve_id": "CVE-2024-5088", "published_date": "2024-05-18T12:15:47.010", "last_modified_date": "2025-01-07T18:03:40.723", "descriptions": [{"lang": "en", "value": "The Happy Addons for Elementor plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘_id’ parameter in all versions up to, and including, 3.10.8 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Happy Addons for Elementor para WordPress es vulnerable a cross site scripting almacenado a través del parámetro '_id' en todas las versiones hasta la 3.10.8 incluida debido a una sanitización de entrada y un escape de salida insuficientes. Esto hace posible que atacantes autenticados, con acceso de nivel de Colaborador y superior, inyecten scripts web arbitrarios en páginas que se ejecutarán cada vez que un usuario acceda a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/happy-elementor-addons/trunk/widgets/skills/widget.php#L360", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3087575/happy-elementor-addons/trunk/widgets/skills/widget.php", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/203ab09f-7344-4cab-86bf-0c1ec545d78f?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}