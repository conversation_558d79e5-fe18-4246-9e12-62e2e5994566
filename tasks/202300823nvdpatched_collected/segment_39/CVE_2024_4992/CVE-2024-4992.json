{"cve_id": "CVE-2024-4992", "published_date": "2024-05-16T12:15:14.950", "last_modified_date": "2024-11-21T09:44:00.787", "descriptions": [{"lang": "en", "value": "Vulnerability in SiAdmin 1.1 that allows SQL injection via the /modul/mod_kuliah/aksi_kuliah.php parameter in nim. This vulnerability could allow a remote attacker to send a specially crafted SQL query to the system and retrieve all the information stored in it."}, {"lang": "es", "value": "Vulnerabilidad en SiAdmin 1.1 que permite la inyección de SQL a través del parámetro /modul/mod_kuliah/aksi_kuliah.php en nim. Esta vulnerabilidad podría permitir a un atacante remoto enviar una consulta SQL especialmente manipulada al sistema y recuperar toda la información almacenada en él."}], "references": [{"url": "https://www.incibe.es/en/incibe-cert/notices/aviso/multiple-vulnerabilities-siadmin", "source": "<EMAIL>", "tags": []}]}